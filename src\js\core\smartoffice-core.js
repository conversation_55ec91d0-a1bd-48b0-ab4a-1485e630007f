/**
 * @file SmartOffice核心模块
 * @description SmartOffice应用的核心基础设施，包含全局配置、命名空间和基础功能
 * <AUTHOR> Team
 * @version 1.0.0
 */

// 创建全局命名空间
window.SmartOffice = window.SmartOffice || {};

/**
 * @namespace SmartOffice.Config
 * @description 全局配置对象
 */
SmartOffice.Config = {
    // 应用信息
    APP_NAME: 'GoMyHire移动端快速透视分析',
    APP_VERSION: '1.0.0',
    
    // 开发设置
    DEBUG: true,
    LOG_LEVEL: 'info', // 'error', 'warn', 'info', 'debug'
    
    // 文件上传设置
    MAX_FILE_SIZE: 5242880, // 5MB
    SUPPORTED_FILE_TYPES: ['csv', 'txt'],
    
    // 数据处理设置
    MAX_ROWS: 10000,
    MAX_FIELDS: 50,
    PREVIEW_ROWS: 10,
    
    // UI设置
    ANIMATION_DURATION: 300,
    TOAST_DURATION: 3000,
    DEBOUNCE_DELAY: 300,
    
    // 存储设置
    STORAGE_PREFIX: 'smartoffice_',
    STORAGE_VERSION: '1.0'
};

/**
 * @namespace SmartOffice.Events
 * @description 事件名称常量
 */
SmartOffice.Events = {
    // 应用生命周期
    APP_INIT: 'app:init',
    APP_READY: 'app:ready',
    APP_ERROR: 'app:error',
    
    // 路由事件
    ROUTE_CHANGE: 'route:change',
    ROUTE_ERROR: 'route:error',
    
    // 配置管理
    CONFIG_CREATE: 'config:create',
    CONFIG_UPDATE: 'config:update',
    CONFIG_DELETE: 'config:delete',
    CONFIG_SELECT: 'config:select',
    
    // 文件处理
    FILE_UPLOAD_START: 'file:upload:start',
    FILE_UPLOAD_PROGRESS: 'file:upload:progress',
    FILE_UPLOAD_COMPLETE: 'file:upload:complete',
    FILE_UPLOAD_ERROR: 'file:upload:error',
    
    // 数据处理
    DATA_PARSE_START: 'data:parse:start',
    DATA_PARSE_COMPLETE: 'data:parse:complete',
    DATA_PARSE_ERROR: 'data:parse:error',
    DATA_VALIDATE: 'data:validate',
    
    // 字段选择
    FIELD_SELECTOR_SHOW: 'fieldSelector:show',
    FIELD_SELECTOR_CONFIRM: 'fieldSelector:confirm',
    FIELD_SELECTOR_CANCEL: 'fieldSelector:cancel',
    
    // 下拉菜单
    DROPDOWN_OPEN: 'dropdown:open',
    DROPDOWN_CLOSE: 'dropdown:close',
    DROPDOWN_SELECT: 'dropdown:select',
    
    // UI交互
    UI_LOADING_SHOW: 'ui:loading:show',
    UI_LOADING_HIDE: 'ui:loading:hide',
    UI_TOAST_SHOW: 'ui:toast:show',
    UI_MODAL_SHOW: 'ui:modal:show',
    UI_MODAL_HIDE: 'ui:modal:hide'
};

/**
 * @namespace SmartOffice.StorageKeys
 * @description 本地存储键名常量
 */
SmartOffice.StorageKeys = {
    CONFIGS: 'configs',
    USER_PREFERENCES: 'userPreferences',
    RECENT_FILES: 'recentFiles',
    APP_STATE: 'appState'
};

/**
 * @namespace SmartOffice.Core
 * @description 核心模块命名空间
 */
SmartOffice.Core = {};

/**
 * @namespace SmartOffice.Utils
 * @description 工具模块命名空间
 */
SmartOffice.Utils = {};

/**
 * @namespace SmartOffice.Data
 * @description 数据处理模块命名空间
 */
SmartOffice.Data = {};

/**
 * @namespace SmartOffice.Components
 * @description UI组件模块命名空间
 */
SmartOffice.Components = {};

/**
 * @function SmartOffice.log
 * @description 统一的日志记录函数
 * @param {string} level - 日志级别 ('error', 'warn', 'info', 'debug')
 * @param {string} message - 日志消息
 * @param {*} data - 可选的附加数据
 */
SmartOffice.log = function(level, message, data) {
    if (!SmartOffice.Config.DEBUG) return;
    
    const levels = ['error', 'warn', 'info', 'debug'];
    const configLevel = SmartOffice.Config.LOG_LEVEL;
    const configLevelIndex = levels.indexOf(configLevel);
    const currentLevelIndex = levels.indexOf(level);
    
    if (currentLevelIndex <= configLevelIndex) {
        const timestamp = new Date().toISOString();
        const prefix = `[${timestamp}] [${level.toUpperCase()}]`;
        
        if (data !== undefined) {
            console[level](prefix, message, data);
        } else {
            console[level](prefix, message);
        }
    }
};

/**
 * @function SmartOffice.triggerHapticFeedback
 * @description 触发触觉反馈 (iOS设备)
 * @param {string} type - 反馈类型 ('light', 'medium', 'heavy')
 */
SmartOffice.triggerHapticFeedback = function(type) {
    try {
        if (window.navigator && window.navigator.vibrate) {
            // Android设备的振动反馈
            const patterns = {
                'light': [10],
                'medium': [20],
                'heavy': [30]
            };
            window.navigator.vibrate(patterns[type] || patterns.light);
        }
        
        // iOS设备的触觉反馈 (如果支持)
        if (window.DeviceMotionEvent && typeof DeviceMotionEvent.requestPermission === 'function') {
            // iOS 13+ 的触觉反馈API (需要用户授权)
            // 这里简化处理，实际项目中可能需要更复杂的实现
        }
    } catch (error) {
        SmartOffice.log('debug', '触觉反馈不支持:', error);
    }
};

/**
 * @function SmartOffice.getVersion
 * @description 获取应用版本信息
 * @returns {Object} 版本信息对象
 */
SmartOffice.getVersion = function() {
    return {
        app: SmartOffice.Config.APP_VERSION,
        storage: SmartOffice.Config.STORAGE_VERSION,
        build: new Date().toISOString().split('T')[0] // 当前日期作为构建版本
    };
};

/**
 * @function SmartOffice.checkBrowserSupport
 * @description 检查浏览器支持情况
 * @returns {Object} 支持情况对象
 */
SmartOffice.checkBrowserSupport = function() {
    const support = {
        localStorage: !!window.localStorage,
        fileReader: !!window.FileReader,
        touchEvents: 'ontouchstart' in window,
        mediaQueries: !!window.matchMedia,
        flexbox: CSS.supports('display', 'flex'),
        grid: CSS.supports('display', 'grid'),
        customProperties: CSS.supports('color', 'var(--test)')
    };
    
    support.isSupported = Object.values(support).every(Boolean);
    return support;
};

// 初始化基础设施
SmartOffice.log('info', 'SmartOffice核心模块初始化完成', {
    version: SmartOffice.getVersion(),
    support: SmartOffice.checkBrowserSupport()
});
