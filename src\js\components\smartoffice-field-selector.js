/**
 * @file SmartOffice字段选择器组件
 * @description iOS风格的字段选择器，支持基于上传数据动态生成字段选择界面
 * <AUTHOR> Team
 * @version 1.0.0
 */

/**
 * @function FieldSelectorComponent
 * @description 字段选择器组件构造函数
 * @constructor
 * @param {Object} options - 配置选项
 * @param {string} options.containerId - 容器元素ID
 * @param {Array} options.fields - 可选字段数组
 * @param {string} options.fieldType - 字段类型 (rowFields, columnFields, valueFields, filterFields)
 * @param {boolean} options.multiple - 是否支持多选（默认true）
 * @param {string} options.title - 选择器标题
 * @param {Function} options.onSelect - 选择回调函数
 * @param {Function} options.onCancel - 取消回调函数
 */
function FieldSelectorComponent(options) {
    // 默认配置
    this.options = SmartOffice.Utils.Helpers.extend({
        containerId: 'fieldSelectorContainer',
        fields: [],
        fieldType: 'rowFields',
        multiple: true,
        title: '选择字段',
        maxSelections: 10,
        showFieldTypes: true,
        onSelect: null,
        onCancel: null
    }, options || {});
    
    // 组件状态
    this.isVisible = false;
    this.selectedFields = [];
    this.availableFields = [];
    
    // DOM元素引用
    this.containerElement = null;
    this.modalElement = null;
    this.backdropElement = null;
    this.headerElement = null;
    this.listElement = null;
    this.footerElement = null;
    
    // 依赖注入
    this.dom = SmartOffice.Utils.DOM;
    this.helpers = SmartOffice.Utils.Helpers;
    this.eventBus = SmartOffice.Core.EventBus;
    this.dataValidator = new SmartOffice.Data.DataValidator();
    
    SmartOffice.log('info', 'FieldSelectorComponent初始化完成');
}

/**
 * @function FieldSelectorComponent.prototype.init
 * @description 初始化字段选择器组件
 * @returns {boolean} 初始化是否成功
 */
FieldSelectorComponent.prototype.init = function() {
    try {
        // 获取容器元素
        this.containerElement = document.getElementById(this.options.containerId);
        if (!this.containerElement) {
            throw new Error('找不到字段选择器容器元素: ' + this.options.containerId);
        }
        
        // 处理字段数据
        this.processFields();
        
        // 渲染组件界面
        this.render();
        
        // 绑定事件监听器
        this.bindEvents();
        
        SmartOffice.log('info', '字段选择器组件初始化成功');
        return true;
    } catch (error) {
        SmartOffice.log('error', '字段选择器组件初始化失败:', error);
        return false;
    }
};

/**
 * @function FieldSelectorComponent.prototype.processFields
 * @description 处理字段数据，添加类型信息
 */
FieldSelectorComponent.prototype.processFields = function() {
    this.availableFields = [];
    
    for (let i = 0; i < this.options.fields.length; i++) {
        const field = this.options.fields[i];
        
        // 如果字段是字符串，转换为对象
        if (typeof field === 'string') {
            this.availableFields.push({
                name: field,
                label: field,
                type: this.detectFieldType(field),
                description: ''
            });
        } else {
            this.availableFields.push({
                name: field.name || field.label,
                label: field.label || field.name,
                type: field.type || this.detectFieldType(field.name),
                description: field.description || ''
            });
        }
    }
    
    SmartOffice.log('info', '已处理字段数据:', this.availableFields.length + '个字段');
};

/**
 * @function FieldSelectorComponent.prototype.detectFieldType
 * @description 检测字段类型
 * @param {string} fieldName - 字段名称
 * @returns {string} 字段类型
 */
FieldSelectorComponent.prototype.detectFieldType = function(fieldName) {
    if (!fieldName || typeof fieldName !== 'string') {
        return 'text';
    }
    
    const name = fieldName.toLowerCase();
    
    // 数值类型字段
    if (name.includes('amount') || name.includes('price') || name.includes('cost') || 
        name.includes('total') || name.includes('sum') || name.includes('count') ||
        name.includes('quantity') || name.includes('qty') || name.includes('number')) {
        return 'number';
    }
    
    // 日期类型字段
    if (name.includes('date') || name.includes('time') || name.includes('created') ||
        name.includes('updated') || name.includes('modified')) {
        return 'date';
    }
    
    // 分类类型字段
    if (name.includes('category') || name.includes('type') || name.includes('status') ||
        name.includes('level') || name.includes('grade') || name.includes('class')) {
        return 'category';
    }
    
    // 默认为文本类型
    return 'text';
};

/**
 * @function FieldSelectorComponent.prototype.render
 * @description 渲染字段选择器界面
 */
FieldSelectorComponent.prototype.render = function() {
    const html = `
        <!-- 背景遮罩 -->
        <div class="so-field-selector-backdrop" id="fieldSelectorBackdrop" style="display: none;"></div>
        
        <!-- 选择器模态框 -->
        <div class="so-field-selector-modal" id="fieldSelectorModal" style="display: none;">
            <!-- 头部 -->
            <div class="so-field-selector-header" id="fieldSelectorHeader">
                <button type="button" class="so-header-button cancel" id="cancelButton">取消</button>
                <h2 class="so-header-title">${this.options.title}</h2>
                <button type="button" class="so-header-button confirm" id="confirmButton">确定</button>
            </div>
            
            <!-- 字段列表 -->
            <div class="so-field-selector-content">
                <div class="so-field-list" id="fieldList">
                    ${this.renderFieldList()}
                </div>
            </div>
            
            <!-- 底部信息 -->
            <div class="so-field-selector-footer" id="fieldSelectorFooter">
                <div class="so-selection-info">
                    <span id="selectionCount">已选择 0 个字段</span>
                    ${this.options.multiple ? `<span class="so-max-info">最多可选择 ${this.options.maxSelections} 个</span>` : ''}
                </div>
            </div>
        </div>
    `;
    
    this.containerElement.innerHTML = html;
    
    // 获取DOM元素引用
    this.modalElement = document.getElementById('fieldSelectorModal');
    this.backdropElement = document.getElementById('fieldSelectorBackdrop');
    this.headerElement = document.getElementById('fieldSelectorHeader');
    this.listElement = document.getElementById('fieldList');
    this.footerElement = document.getElementById('fieldSelectorFooter');
};

/**
 * @function FieldSelectorComponent.prototype.renderFieldList
 * @description 渲染字段列表
 * @returns {string} 字段列表HTML
 */
FieldSelectorComponent.prototype.renderFieldList = function() {
    if (this.availableFields.length === 0) {
        return '<div class="so-field-empty">暂无可选字段</div>';
    }
    
    let html = '';
    for (let i = 0; i < this.availableFields.length; i++) {
        const field = this.availableFields[i];
        const isSelected = this.isFieldSelected(field);
        
        html += `
            <div class="so-field-item ${isSelected ? 'selected' : ''}" 
                 data-field-name="${this.helpers.escapeHtml(field.name)}"
                 data-field-index="${i}">
                <div class="so-field-content">
                    <div class="so-field-main">
                        <span class="so-field-name">${this.helpers.escapeHtml(field.label)}</span>
                        ${this.options.showFieldTypes ? this.renderFieldTypeTag(field.type) : ''}
                    </div>
                    ${field.description ? `<div class="so-field-description">${this.helpers.escapeHtml(field.description)}</div>` : ''}
                </div>
                <div class="so-field-selector">
                    ${this.options.multiple ? this.renderCheckbox(isSelected) : this.renderRadio(isSelected)}
                </div>
            </div>
        `;
    }
    
    return html;
};

/**
 * @function FieldSelectorComponent.prototype.renderFieldTypeTag
 * @description 渲染字段类型标签
 * @param {string} type - 字段类型
 * @returns {string} 类型标签HTML
 */
FieldSelectorComponent.prototype.renderFieldTypeTag = function(type) {
    const typeLabels = {
        'number': '数值',
        'date': '日期',
        'time': '时间',
        'string': '文本'
    };

    const label = typeLabels[type] || '文本';
    return `<span class="so-field-type so-field-type-${type}">${label}</span>`;
};

/**
 * @function FieldSelectorComponent.prototype.renderCheckbox
 * @description 渲染复选框
 * @param {boolean} checked - 是否选中
 * @returns {string} 复选框HTML
 */
FieldSelectorComponent.prototype.renderCheckbox = function(checked) {
    return `
        <span class="so-field-checkbox ${checked ? 'checked' : ''}">
            <svg viewBox="0 0 24 24" class="checkbox-icon">
                <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
            </svg>
        </span>
    `;
};

/**
 * @function FieldSelectorComponent.prototype.renderRadio
 * @description 渲染单选框
 * @param {boolean} checked - 是否选中
 * @returns {string} 单选框HTML
 */
FieldSelectorComponent.prototype.renderRadio = function(checked) {
    return `
        <span class="so-field-radio ${checked ? 'checked' : ''}">
            <span class="so-radio-inner"></span>
        </span>
    `;
};

/**
 * @function FieldSelectorComponent.prototype.bindEvents
 * @description 绑定事件监听器
 */
FieldSelectorComponent.prototype.bindEvents = function() {
    const self = this;

    // 取消按钮事件
    const cancelButton = document.getElementById('cancelButton');
    if (cancelButton) {
        cancelButton.addEventListener('click', function() {
            self.cancel();
        });
        this.dom.addTouchFeedback(cancelButton, 'light');
    }

    // 确定按钮事件
    const confirmButton = document.getElementById('confirmButton');
    if (confirmButton) {
        confirmButton.addEventListener('click', function() {
            self.confirm();
        });
        this.dom.addTouchFeedback(confirmButton, 'light');
    }

    // 背景遮罩点击事件
    if (this.backdropElement) {
        this.backdropElement.addEventListener('click', function() {
            self.cancel();
        });
    }

    // 字段项点击事件
    if (this.listElement) {
        this.listElement.addEventListener('click', function(event) {
            const fieldItem = event.target.closest('.so-field-item');
            if (fieldItem) {
                event.preventDefault();
                event.stopPropagation();

                const fieldName = fieldItem.dataset.fieldName;
                const fieldIndex = parseInt(fieldItem.dataset.fieldIndex);
                self.toggleField(self.availableFields[fieldIndex]);
            }
        });
    }

    // 键盘事件
    document.addEventListener('keydown', function(event) {
        if (self.isVisible) {
            if (event.key === 'Escape') {
                self.cancel();
            } else if (event.key === 'Enter') {
                self.confirm();
            }
        }
    });
};

/**
 * @function FieldSelectorComponent.prototype.show
 * @description 显示字段选择器
 * @param {Array} preSelectedFields - 预选字段
 */
FieldSelectorComponent.prototype.show = function(preSelectedFields) {
    if (this.isVisible) return;

    // 设置预选字段
    this.selectedFields = preSelectedFields ? [...preSelectedFields] : [];

    // 显示模态框
    this.isVisible = true;
    this.dom.setStyle(this.backdropElement, { display: 'block' });
    this.dom.setStyle(this.modalElement, { display: 'block' });

    // 添加显示动画类
    setTimeout(() => {
        this.dom.addClass(this.backdropElement, 'so-backdrop-visible');
        this.dom.addClass(this.modalElement, 'so-modal-visible');
    }, 10);

    // 更新界面状态
    this.updateFieldStates();
    this.updateSelectionInfo();

    // 阻止背景滚动
    this.dom.addClass(document.body, 'so-modal-open');

    SmartOffice.log('info', '字段选择器已显示');
};

/**
 * @function FieldSelectorComponent.prototype.hide
 * @description 隐藏字段选择器
 */
FieldSelectorComponent.prototype.hide = function() {
    if (!this.isVisible) return;

    this.isVisible = false;

    // 移除显示动画类
    this.dom.removeClass(this.backdropElement, 'so-backdrop-visible');
    this.dom.removeClass(this.modalElement, 'so-modal-visible');

    // 延迟隐藏元素
    setTimeout(() => {
        this.dom.setStyle(this.backdropElement, { display: 'none' });
        this.dom.setStyle(this.modalElement, { display: 'none' });
    }, 300);

    // 恢复背景滚动
    this.dom.removeClass(document.body, 'so-modal-open');

    SmartOffice.log('info', '字段选择器已隐藏');
};

/**
 * @function FieldSelectorComponent.prototype.toggleField
 * @description 切换字段选择状态
 * @param {Object} field - 字段对象
 */
FieldSelectorComponent.prototype.toggleField = function(field) {
    if (!field) return;

    const index = this.selectedFields.findIndex(selected => selected.name === field.name);

    if (index > -1) {
        // 取消选择
        this.selectedFields.splice(index, 1);
    } else {
        // 添加选择
        if (this.options.multiple) {
            // 多选模式：检查数量限制
            if (this.selectedFields.length >= this.options.maxSelections) {
                this.eventBus.emit(SmartOffice.Events.UI_TOAST_SHOW,
                    `最多只能选择${this.options.maxSelections}个字段`, 'warning');
                return;
            }
            this.selectedFields.push(field);
        } else {
            // 单选模式：替换选择
            this.selectedFields = [field];
        }
    }

    // 更新界面状态
    this.updateFieldStates();
    this.updateSelectionInfo();

    // 触发触觉反馈
    SmartOffice.triggerHapticFeedback('light');

    SmartOffice.log('info', '字段选择状态已切换:', field.name);
};

/**
 * @function FieldSelectorComponent.prototype.updateFieldStates
 * @description 更新字段状态显示
 */
FieldSelectorComponent.prototype.updateFieldStates = function() {
    const fieldItems = this.listElement.querySelectorAll('.so-field-item');

    for (let i = 0; i < fieldItems.length; i++) {
        const item = fieldItems[i];
        const fieldName = item.dataset.fieldName;
        const isSelected = this.selectedFields.some(field => field.name === fieldName);

        if (isSelected) {
            this.dom.addClass(item, 'selected');
        } else {
            this.dom.removeClass(item, 'selected');
        }

        // 更新选择器状态
        const selector = item.querySelector('.so-field-checkbox, .so-field-radio');
        if (selector) {
            if (isSelected) {
                this.dom.addClass(selector, 'checked');
            } else {
                this.dom.removeClass(selector, 'checked');
            }
        }
    }
};

/**
 * @function FieldSelectorComponent.prototype.updateSelectionInfo
 * @description 更新选择信息显示
 */
FieldSelectorComponent.prototype.updateSelectionInfo = function() {
    const selectionCount = document.getElementById('selectionCount');
    if (selectionCount) {
        const count = this.selectedFields.length;
        if (this.options.multiple) {
            selectionCount.textContent = `已选择 ${count} 个字段`;
        } else {
            selectionCount.textContent = count > 0 ? `已选择 1 个字段` : '请选择字段';
        }
    }
};

/**
 * @function FieldSelectorComponent.prototype.isFieldSelected
 * @description 检查字段是否已选择
 * @param {Object} field - 字段对象
 * @returns {boolean} 是否已选择
 */
FieldSelectorComponent.prototype.isFieldSelected = function(field) {
    return this.selectedFields.some(selected => selected.name === field.name);
};

/**
 * @function FieldSelectorComponent.prototype.confirm
 * @description 确认选择
 */
FieldSelectorComponent.prototype.confirm = function() {
    // 验证选择
    if (this.selectedFields.length === 0) {
        this.eventBus.emit(SmartOffice.Events.UI_TOAST_SHOW, '请至少选择一个字段', 'warning');
        return;
    }

    // 触发选择事件
    if (this.options.onSelect) {
        this.options.onSelect(this.selectedFields, this.options.fieldType);
    }

    this.eventBus.emit(SmartOffice.Events.FIELD_SELECTOR_CONFIRM, {
        fields: this.selectedFields,
        fieldType: this.options.fieldType
    });

    // 隐藏选择器
    this.hide();

    SmartOffice.log('info', '字段选择已确认:', this.selectedFields.length + '个字段');
};

/**
 * @function FieldSelectorComponent.prototype.cancel
 * @description 取消选择
 */
FieldSelectorComponent.prototype.cancel = function() {
    // 触发取消事件
    if (this.options.onCancel) {
        this.options.onCancel();
    }

    this.eventBus.emit(SmartOffice.Events.FIELD_SELECTOR_CANCEL, {
        fieldType: this.options.fieldType
    });

    // 隐藏选择器
    this.hide();

    SmartOffice.log('info', '字段选择已取消');
};

/**
 * @function FieldSelectorComponent.prototype.setFields
 * @description 设置可选字段
 * @param {Array} fields - 字段数组
 */
FieldSelectorComponent.prototype.setFields = function(fields) {
    this.options.fields = fields || [];
    this.processFields();

    // 重新渲染字段列表
    if (this.listElement) {
        this.listElement.innerHTML = this.renderFieldList();
    }

    // 清空当前选择
    this.selectedFields = [];
    this.updateSelectionInfo();

    SmartOffice.log('info', '字段选择器字段已更新:', this.availableFields.length + '个字段');
};

/**
 * @function FieldSelectorComponent.prototype.getSelectedFields
 * @description 获取选中的字段
 * @returns {Array} 选中的字段数组
 */
FieldSelectorComponent.prototype.getSelectedFields = function() {
    return [...this.selectedFields];
};

/**
 * @function FieldSelectorComponent.prototype.setSelectedFields
 * @description 设置选中的字段
 * @param {Array} fields - 要选中的字段数组
 */
FieldSelectorComponent.prototype.setSelectedFields = function(fields) {
    this.selectedFields = fields ? [...fields] : [];
    this.updateFieldStates();
    this.updateSelectionInfo();
};

/**
 * @function FieldSelectorComponent.prototype.clearSelection
 * @description 清空选择
 */
FieldSelectorComponent.prototype.clearSelection = function() {
    this.selectedFields = [];
    this.updateFieldStates();
    this.updateSelectionInfo();
};

/**
 * @function FieldSelectorComponent.prototype.destroy
 * @description 销毁组件
 */
FieldSelectorComponent.prototype.destroy = function() {
    try {
        // 隐藏选择器
        if (this.isVisible) {
            this.hide();
        }

        // 移除事件监听器
        const cancelButton = document.getElementById('cancelButton');
        const confirmButton = document.getElementById('confirmButton');

        if (cancelButton) {
            cancelButton.removeEventListener('click', this.cancel);
        }

        if (confirmButton) {
            confirmButton.removeEventListener('click', this.confirm);
        }

        if (this.backdropElement) {
            this.backdropElement.removeEventListener('click', this.cancel);
        }

        // 清空容器
        if (this.containerElement) {
            this.containerElement.innerHTML = '';
        }

        // 清理引用
        this.containerElement = null;
        this.modalElement = null;
        this.backdropElement = null;
        this.headerElement = null;
        this.listElement = null;
        this.footerElement = null;
        this.selectedFields = [];
        this.availableFields = [];

        SmartOffice.log('info', '字段选择器组件已销毁');

    } catch (error) {
        SmartOffice.log('error', '字段选择器组件销毁失败:', error);
    }
};

/**
 * @function FieldSelectorComponent.prototype.refresh
 * @description 刷新组件
 */
FieldSelectorComponent.prototype.refresh = function() {
    if (this.listElement) {
        this.listElement.innerHTML = this.renderFieldList();
    }
    this.updateFieldStates();
    this.updateSelectionInfo();
};

/**
 * @function FieldSelectorComponent.prototype.isVisible
 * @description 检查选择器是否可见
 * @returns {boolean} 是否可见
 */
FieldSelectorComponent.prototype.isSelectorVisible = function() {
    return this.isVisible;
};

// 注册到全局命名空间
SmartOffice.Components.FieldSelector = FieldSelectorComponent;

SmartOffice.log('info', 'SmartOffice字段选择器组件模块初始化完成');
