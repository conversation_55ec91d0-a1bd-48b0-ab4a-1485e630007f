/**
 * @file SmartOffice字段选择器组件
 * @description iOS风格的字段选择器，支持基于上传数据动态生成字段选择界面
 * <AUTHOR> Team
 * @version 1.0.0
 */

/**
 * @function FieldSelectorComponent
 * @description 字段选择器组件构造函数
 * @constructor
 * @param {Object} options - 配置选项
 * @param {string} options.containerId - 容器元素ID
 * @param {Array} options.fields - 可选字段数组
 * @param {string} options.fieldType - 字段类型 (rowFields, columnFields, valueFields, filterFields)
 * @param {boolean} options.multiple - 是否支持多选（默认true）
 * @param {string} options.title - 选择器标题
 * @param {Function} options.onSelect - 选择回调函数
 * @param {Function} options.onCancel - 取消回调函数
 */
function FieldSelectorComponent(options) {
    // 默认配置
    this.options = SmartOffice.Utils.Helpers.extend({
        containerId: 'fieldSelectorContainer',
        fields: [],
        fieldType: 'rowFields',
        multiple: true,
        title: '选择字段',
        maxSelections: 10,
        showFieldTypes: true,
        onSelect: null,
        onCancel: null
    }, options || {});
    
    // 组件状态
    this.isVisible = false;
    this.selectedFields = [];
    this.availableFields = [];
    
    // DOM元素引用
    this.containerElement = null;
    this.modalElement = null;
    this.backdropElement = null;
    this.headerElement = null;
    this.listElement = null;
    this.footerElement = null;
    
    // 依赖注入
    this.dom = SmartOffice.Utils.DOM;
    this.helpers = SmartOffice.Utils.Helpers;
    this.eventBus = SmartOffice.Core.EventBus;
    this.dataValidator = new SmartOffice.Data.DataValidator();
    
    SmartOffice.log('info', 'FieldSelectorComponent初始化完成');
}

/**
 * @function FieldSelectorComponent.prototype.init
 * @description 初始化字段选择器组件
 * @returns {boolean} 初始化是否成功
 */
FieldSelectorComponent.prototype.init = function() {
    try {
        // 获取容器元素
        this.containerElement = document.getElementById(this.options.containerId);
        if (!this.containerElement) {
            throw new Error('找不到字段选择器容器元素: ' + this.options.containerId);
        }
        
        // 处理字段数据
        this.processFields();
        
        // 渲染组件界面
        this.render();
        
        // 绑定事件监听器
        this.bindEvents();
        
        SmartOffice.log('info', '字段选择器组件初始化成功');
        return true;
    } catch (error) {
        SmartOffice.log('error', '字段选择器组件初始化失败:', error);
        return false;
    }
};

/**
 * @function FieldSelectorComponent.prototype.processFields
 * @description 处理字段数据，添加类型信息
 */
FieldSelectorComponent.prototype.processFields = function() {
    this.availableFields = [];

    for (let i = 0; i < this.options.fields.length; i++) {
        const field = this.options.fields[i];

        // 如果字段是字符串，转换为对象
        if (typeof field === 'string') {
            this.availableFields.push({
                name: field,
                label: field,
                type: this.detectFieldType(field),
                description: ''
            });
        } else {
            this.availableFields.push({
                name: field.name || field.label,
                label: field.label || field.name,
                type: field.type || this.detectFieldType(field.name),
                description: field.description || ''
            });
        }
    }

    SmartOffice.log('info', '已处理字段数据:', this.availableFields.length + '个字段');
};

/**
 * @function FieldSelectorComponent.prototype.setDataSource
 * @description 设置数据源用于字段类型检测
 * @param {Object} dataSource - 数据源对象（包含data和headers）
 */
FieldSelectorComponent.prototype.setDataSource = function(dataSource) {
    this.dataSource = dataSource;

    // 重新处理字段类型
    if (this.options.fields && this.options.fields.length > 0) {
        this.processFields();

        // 如果选择器已经渲染，重新渲染字段列表
        if (this.listElement) {
            this.listElement.innerHTML = this.renderFieldList();
        }
    }

    SmartOffice.log('info', '数据源已设置，字段类型已更新');
};

/**
 * @function FieldSelectorComponent.prototype.detectFieldType
 * @description 检测字段类型 - 使用数据验证器进行准确的类型检测
 * @param {string} fieldName - 字段名称
 * @returns {string} 字段类型
 */
FieldSelectorComponent.prototype.detectFieldType = function(fieldName) {
    if (!fieldName || typeof fieldName !== 'string') {
        return 'string';
    }

    // 如果有可用的数据样本，使用数据验证器进行准确检测
    if (this.dataSource && this.dataSource.data && this.dataSource.data.length > 0) {
        if (!this.dataValidator) {
            this.dataValidator = new SmartOffice.Data.DataValidator();
        }

        // 分析前几行数据来确定字段类型
        const sampleSize = Math.min(this.dataSource.data.length, 10);
        const typeCounts = { string: 0, number: 0, date: 0, time: 0, empty: 0 };

        for (let i = 0; i < sampleSize; i++) {
            const value = this.dataSource.data[i][fieldName];
            const type = this.dataValidator.detectDataType(value);
            typeCounts[type] = (typeCounts[type] || 0) + 1;
        }

        // 找出主要类型
        let dominantType = 'string';
        let maxCount = 0;
        for (const type in typeCounts) {
            if (type !== 'empty' && typeCounts[type] > maxCount) {
                maxCount = typeCounts[type];
                dominantType = type;
            }
        }

        return dominantType;
    }

    // 如果没有数据样本，使用基于字段名称的简单检测作为后备
    const name = fieldName.toLowerCase();

    // 数值类型字段
    if (name.includes('amount') || name.includes('price') || name.includes('cost') ||
        name.includes('total') || name.includes('sum') || name.includes('count') ||
        name.includes('quantity') || name.includes('qty') || name.includes('number') ||
        name.includes('销售额') || name.includes('金额') || name.includes('数量')) {
        return 'number';
    }

    // 时间类型字段
    if (name.includes('time') || name.includes('时间')) {
        return 'time';
    }

    // 日期类型字段
    if (name.includes('date') || name.includes('created') || name.includes('updated') ||
        name.includes('modified') || name.includes('日期')) {
        return 'date';
    }

    // 默认为文本类型
    return 'string';
};

/**
 * @function FieldSelectorComponent.prototype.render
 * @description 渲染字段选择器界面
 */
FieldSelectorComponent.prototype.render = function() {
    const html = `
        <!-- 背景遮罩 -->
        <div class="so-field-selector-backdrop" id="fieldSelectorBackdrop" style="display: none;"></div>
        
        <!-- 选择器模态框 -->
        <div class="so-field-selector-modal" id="fieldSelectorModal" style="display: none;">
            <!-- 头部 -->
            <div class="so-field-selector-header" id="fieldSelectorHeader">
                <button type="button" class="so-header-button cancel" id="cancelButton">取消</button>
                <h2 class="so-header-title">${this.options.title}</h2>
                <button type="button" class="so-header-button confirm" id="confirmButton">确定</button>
            </div>
            
            <!-- 字段列表 -->
            <div class="so-field-selector-content">
                <div class="so-field-list" id="fieldList">
                    ${this.renderFieldList()}
                </div>
            </div>
            
            <!-- 底部信息 -->
            <div class="so-field-selector-footer" id="fieldSelectorFooter">
                <div class="so-selection-info">
                    <span id="selectionCount">已选择 0 个字段</span>
                    ${this.options.multiple ? `<span class="so-max-info">最多可选择 ${this.options.maxSelections} 个</span>` : ''}
                </div>
            </div>
        </div>
    `;
    
    this.containerElement.innerHTML = html;
    
    // 获取DOM元素引用
    this.modalElement = document.getElementById('fieldSelectorModal');
    this.backdropElement = document.getElementById('fieldSelectorBackdrop');
    this.headerElement = document.getElementById('fieldSelectorHeader');
    this.listElement = document.getElementById('fieldList');
    this.footerElement = document.getElementById('fieldSelectorFooter');
};

/**
 * @function FieldSelectorComponent.prototype.renderFieldList
 * @description 渲染字段列表
 * @returns {string} 字段列表HTML
 */
FieldSelectorComponent.prototype.renderFieldList = function() {
    if (this.availableFields.length === 0) {
        return '<div class="so-field-empty">暂无可选字段</div>';
    }
    
    let html = '';
    for (let i = 0; i < this.availableFields.length; i++) {
        const field = this.availableFields[i];
        const isSelected = this.isFieldSelected(field);
        
        html += `
            <div class="so-field-item ${isSelected ? 'selected' : ''}"
                 data-field-name="${this.helpers.escapeHtml(field.name)}"
                 data-field-index="${i}">
                <div class="so-field-content">
                    <div class="so-field-main">
                        <span class="so-field-name">${this.helpers.escapeHtml(field.label)}</span>
                        ${this.options.showFieldTypes ? this.renderFieldTypeTag(field.type) : ''}
                        ${field.type === 'time' ? this.renderTimeRangeButton(field) : ''}
                    </div>
                    ${field.description ? `<div class="so-field-description">${this.helpers.escapeHtml(field.description)}</div>` : ''}
                    ${field.type === 'time' && field.timeRanges ? this.renderTimeRangeDisplay(field.timeRanges) : ''}
                </div>
                <div class="so-field-selector">
                    ${this.options.multiple ? this.renderCheckbox(isSelected) : this.renderRadio(isSelected)}
                </div>
            </div>
        `;
    }
    
    return html;
};

/**
 * @function FieldSelectorComponent.prototype.renderFieldTypeTag
 * @description 渲染字段类型标签
 * @param {string} type - 字段类型
 * @returns {string} 类型标签HTML
 */
FieldSelectorComponent.prototype.renderFieldTypeTag = function(type) {
    const typeLabels = {
        'number': '数值',
        'date': '日期',
        'time': '时间',
        'string': '文本',
        'text': '文本'  // 兼容旧的text类型
    };

    const label = typeLabels[type] || '文本';
    return `<span class="so-field-type so-field-type-${type}">${label}</span>`;
};

/**
 * @function FieldSelectorComponent.prototype.renderTimeRangeButton
 * @description 渲染时间区间设置按钮
 * @param {Object} field - 字段对象
 * @returns {string} 时间区间按钮HTML
 */
FieldSelectorComponent.prototype.renderTimeRangeButton = function(field) {
    return `
        <button type="button"
                class="so-time-range-btn"
                data-field-name="${this.helpers.escapeHtml(field.name)}"
                title="设置时间区间">
            <svg viewBox="0 0 24 24" class="time-range-icon">
                <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M16.2,16.2L11,13V7H12.5V12.2L17,14.9L16.2,16.2Z"/>
            </svg>
        </button>
    `;
};

/**
 * @function FieldSelectorComponent.prototype.renderTimeRangeDisplay
 * @description 渲染时间区间显示
 * @param {Array} timeRanges - 时间区间数组
 * @returns {string} 时间区间显示HTML
 */
FieldSelectorComponent.prototype.renderTimeRangeDisplay = function(timeRanges) {
    if (!timeRanges || timeRanges.length === 0) {
        return '';
    }

    let html = '<div class="so-time-ranges">';
    for (let i = 0; i < timeRanges.length; i++) {
        const range = timeRanges[i];
        html += `
            <span class="so-time-range-tag">
                ${range.name || `${range.start}-${range.end}`}
            </span>
        `;
    }
    html += '</div>';

    return html;
};

/**
 * @function FieldSelectorComponent.prototype.renderCheckbox
 * @description 渲染复选框
 * @param {boolean} checked - 是否选中
 * @returns {string} 复选框HTML
 */
FieldSelectorComponent.prototype.renderCheckbox = function(checked) {
    return `
        <span class="so-field-checkbox ${checked ? 'checked' : ''}">
            <svg viewBox="0 0 24 24" class="checkbox-icon">
                <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
            </svg>
        </span>
    `;
};

/**
 * @function FieldSelectorComponent.prototype.renderRadio
 * @description 渲染单选框
 * @param {boolean} checked - 是否选中
 * @returns {string} 单选框HTML
 */
FieldSelectorComponent.prototype.renderRadio = function(checked) {
    return `
        <span class="so-field-radio ${checked ? 'checked' : ''}">
            <span class="so-radio-inner"></span>
        </span>
    `;
};

/**
 * @function FieldSelectorComponent.prototype.bindEvents
 * @description 绑定事件监听器
 */
FieldSelectorComponent.prototype.bindEvents = function() {
    const self = this;

    // 取消按钮事件
    const cancelButton = document.getElementById('cancelButton');
    if (cancelButton) {
        cancelButton.addEventListener('click', function() {
            self.cancel();
        });
        this.dom.addTouchFeedback(cancelButton, 'light');
    }

    // 确定按钮事件
    const confirmButton = document.getElementById('confirmButton');
    if (confirmButton) {
        confirmButton.addEventListener('click', function() {
            self.confirm();
        });
        this.dom.addTouchFeedback(confirmButton, 'light');
    }

    // 背景遮罩点击事件
    if (this.backdropElement) {
        this.backdropElement.addEventListener('click', function() {
            self.cancel();
        });
    }

    // 字段项点击事件
    if (this.listElement) {
        this.listElement.addEventListener('click', function(event) {
            // 检查是否点击了时间区间按钮
            const timeRangeBtn = event.target.closest('.so-time-range-btn');
            if (timeRangeBtn) {
                event.preventDefault();
                event.stopPropagation();

                const fieldName = timeRangeBtn.dataset.fieldName;
                const field = self.availableFields.find(f => f.name === fieldName);
                if (field) {
                    self.showTimeRangeModal(field);
                }
                return;
            }

            const fieldItem = event.target.closest('.so-field-item');
            if (fieldItem) {
                event.preventDefault();
                event.stopPropagation();

                const fieldName = fieldItem.dataset.fieldName;
                const fieldIndex = parseInt(fieldItem.dataset.fieldIndex);
                self.toggleField(self.availableFields[fieldIndex]);
            }
        });
    }

    // 键盘事件
    document.addEventListener('keydown', function(event) {
        if (self.isVisible) {
            if (event.key === 'Escape') {
                self.cancel();
            } else if (event.key === 'Enter') {
                self.confirm();
            }
        }
    });
};

/**
 * @function FieldSelectorComponent.prototype.show
 * @description 显示字段选择器
 * @param {Array} preSelectedFields - 预选字段
 */
FieldSelectorComponent.prototype.show = function(preSelectedFields) {
    if (this.isVisible) return;

    // 设置预选字段
    this.selectedFields = preSelectedFields ? [...preSelectedFields] : [];

    // 显示模态框
    this.isVisible = true;
    this.dom.setStyle(this.backdropElement, { display: 'block' });
    this.dom.setStyle(this.modalElement, { display: 'block' });

    // 添加显示动画类
    setTimeout(() => {
        this.dom.addClass(this.backdropElement, 'so-backdrop-visible');
        this.dom.addClass(this.modalElement, 'so-modal-visible');
    }, 10);

    // 更新界面状态
    this.updateFieldStates();
    this.updateSelectionInfo();

    // 阻止背景滚动
    this.dom.addClass(document.body, 'so-modal-open');

    SmartOffice.log('info', '字段选择器已显示');
};

/**
 * @function FieldSelectorComponent.prototype.hide
 * @description 隐藏字段选择器
 */
FieldSelectorComponent.prototype.hide = function() {
    if (!this.isVisible) return;

    this.isVisible = false;

    // 移除显示动画类
    this.dom.removeClass(this.backdropElement, 'so-backdrop-visible');
    this.dom.removeClass(this.modalElement, 'so-modal-visible');

    // 延迟隐藏元素
    setTimeout(() => {
        this.dom.setStyle(this.backdropElement, { display: 'none' });
        this.dom.setStyle(this.modalElement, { display: 'none' });
    }, 300);

    // 恢复背景滚动
    this.dom.removeClass(document.body, 'so-modal-open');

    SmartOffice.log('info', '字段选择器已隐藏');
};

/**
 * @function FieldSelectorComponent.prototype.toggleField
 * @description 切换字段选择状态
 * @param {Object} field - 字段对象
 */
FieldSelectorComponent.prototype.toggleField = function(field) {
    if (!field) return;

    const index = this.selectedFields.findIndex(selected => selected.name === field.name);

    if (index > -1) {
        // 取消选择
        this.selectedFields.splice(index, 1);
    } else {
        // 添加选择
        if (this.options.multiple) {
            // 多选模式：检查数量限制
            if (this.selectedFields.length >= this.options.maxSelections) {
                this.eventBus.emit(SmartOffice.Events.UI_TOAST_SHOW,
                    `最多只能选择${this.options.maxSelections}个字段`, 'warning');
                return;
            }
            this.selectedFields.push(field);
        } else {
            // 单选模式：替换选择
            this.selectedFields = [field];
        }
    }

    // 更新界面状态
    this.updateFieldStates();
    this.updateSelectionInfo();

    // 触发触觉反馈
    SmartOffice.triggerHapticFeedback('light');

    SmartOffice.log('info', '字段选择状态已切换:', field.name);
};

/**
 * @function FieldSelectorComponent.prototype.updateFieldStates
 * @description 更新字段状态显示
 */
FieldSelectorComponent.prototype.updateFieldStates = function() {
    const fieldItems = this.listElement.querySelectorAll('.so-field-item');

    for (let i = 0; i < fieldItems.length; i++) {
        const item = fieldItems[i];
        const fieldName = item.dataset.fieldName;
        const isSelected = this.selectedFields.some(field => field.name === fieldName);

        if (isSelected) {
            this.dom.addClass(item, 'selected');
        } else {
            this.dom.removeClass(item, 'selected');
        }

        // 更新选择器状态
        const selector = item.querySelector('.so-field-checkbox, .so-field-radio');
        if (selector) {
            if (isSelected) {
                this.dom.addClass(selector, 'checked');
            } else {
                this.dom.removeClass(selector, 'checked');
            }
        }
    }
};

/**
 * @function FieldSelectorComponent.prototype.updateSelectionInfo
 * @description 更新选择信息显示
 */
FieldSelectorComponent.prototype.updateSelectionInfo = function() {
    const selectionCount = document.getElementById('selectionCount');
    if (selectionCount) {
        const count = this.selectedFields.length;
        if (this.options.multiple) {
            selectionCount.textContent = `已选择 ${count} 个字段`;
        } else {
            selectionCount.textContent = count > 0 ? `已选择 1 个字段` : '请选择字段';
        }
    }
};

/**
 * @function FieldSelectorComponent.prototype.isFieldSelected
 * @description 检查字段是否已选择
 * @param {Object} field - 字段对象
 * @returns {boolean} 是否已选择
 */
FieldSelectorComponent.prototype.isFieldSelected = function(field) {
    return this.selectedFields.some(selected => selected.name === field.name);
};

/**
 * @function FieldSelectorComponent.prototype.confirm
 * @description 确认选择
 */
FieldSelectorComponent.prototype.confirm = function() {
    // 验证选择
    if (this.selectedFields.length === 0) {
        this.eventBus.emit(SmartOffice.Events.UI_TOAST_SHOW, '请至少选择一个字段', 'warning');
        return;
    }

    // 触发选择事件
    if (this.options.onSelect) {
        this.options.onSelect(this.selectedFields, this.options.fieldType);
    }

    this.eventBus.emit(SmartOffice.Events.FIELD_SELECTOR_CONFIRM, {
        fields: this.selectedFields,
        fieldType: this.options.fieldType
    });

    // 隐藏选择器
    this.hide();

    SmartOffice.log('info', '字段选择已确认:', this.selectedFields.length + '个字段');
};

/**
 * @function FieldSelectorComponent.prototype.cancel
 * @description 取消选择
 */
FieldSelectorComponent.prototype.cancel = function() {
    // 触发取消事件
    if (this.options.onCancel) {
        this.options.onCancel();
    }

    this.eventBus.emit(SmartOffice.Events.FIELD_SELECTOR_CANCEL, {
        fieldType: this.options.fieldType
    });

    // 隐藏选择器
    this.hide();

    SmartOffice.log('info', '字段选择已取消');
};

/**
 * @function FieldSelectorComponent.prototype.setFields
 * @description 设置可选字段
 * @param {Array} fields - 字段数组
 */
FieldSelectorComponent.prototype.setFields = function(fields) {
    this.options.fields = fields || [];
    this.processFields();

    // 重新渲染字段列表
    if (this.listElement) {
        this.listElement.innerHTML = this.renderFieldList();
    }

    // 清空当前选择
    this.selectedFields = [];
    this.updateSelectionInfo();

    SmartOffice.log('info', '字段选择器字段已更新:', this.availableFields.length + '个字段');
};

/**
 * @function FieldSelectorComponent.prototype.getSelectedFields
 * @description 获取选中的字段
 * @returns {Array} 选中的字段数组
 */
FieldSelectorComponent.prototype.getSelectedFields = function() {
    return [...this.selectedFields];
};

/**
 * @function FieldSelectorComponent.prototype.setSelectedFields
 * @description 设置选中的字段
 * @param {Array} fields - 要选中的字段数组
 */
FieldSelectorComponent.prototype.setSelectedFields = function(fields) {
    this.selectedFields = fields ? [...fields] : [];
    this.updateFieldStates();
    this.updateSelectionInfo();
};

/**
 * @function FieldSelectorComponent.prototype.clearSelection
 * @description 清空选择
 */
FieldSelectorComponent.prototype.clearSelection = function() {
    this.selectedFields = [];
    this.updateFieldStates();
    this.updateSelectionInfo();
};

/**
 * @function FieldSelectorComponent.prototype.destroy
 * @description 销毁组件
 */
FieldSelectorComponent.prototype.destroy = function() {
    try {
        // 隐藏选择器
        if (this.isVisible) {
            this.hide();
        }

        // 移除事件监听器
        const cancelButton = document.getElementById('cancelButton');
        const confirmButton = document.getElementById('confirmButton');

        if (cancelButton) {
            cancelButton.removeEventListener('click', this.cancel);
        }

        if (confirmButton) {
            confirmButton.removeEventListener('click', this.confirm);
        }

        if (this.backdropElement) {
            this.backdropElement.removeEventListener('click', this.cancel);
        }

        // 清空容器
        if (this.containerElement) {
            this.containerElement.innerHTML = '';
        }

        // 清理引用
        this.containerElement = null;
        this.modalElement = null;
        this.backdropElement = null;
        this.headerElement = null;
        this.listElement = null;
        this.footerElement = null;
        this.selectedFields = [];
        this.availableFields = [];

        SmartOffice.log('info', '字段选择器组件已销毁');

    } catch (error) {
        SmartOffice.log('error', '字段选择器组件销毁失败:', error);
    }
};

/**
 * @function FieldSelectorComponent.prototype.refresh
 * @description 刷新组件
 */
FieldSelectorComponent.prototype.refresh = function() {
    if (this.listElement) {
        this.listElement.innerHTML = this.renderFieldList();
    }
    this.updateFieldStates();
    this.updateSelectionInfo();
};

/**
 * @function FieldSelectorComponent.prototype.isSelectorVisible
 * @description 检查选择器是否可见
 * @returns {boolean} 是否可见
 */
FieldSelectorComponent.prototype.isSelectorVisible = function() {
    return this.isVisible;
};

/**
 * @function FieldSelectorComponent.prototype.showTimeRangeModal
 * @description 显示时间区间设置模态框
 * @param {Object} field - 时间字段对象
 */
FieldSelectorComponent.prototype.showTimeRangeModal = function(field) {
    // 触发触觉反馈
    SmartOffice.triggerHapticFeedback('light');

    // 创建时间区间设置模态框
    this.createTimeRangeModal(field);

    SmartOffice.log('info', '显示时间区间设置模态框:', field.name);
};

/**
 * @function FieldSelectorComponent.prototype.createTimeRangeModal
 * @description 创建时间区间设置模态框
 * @param {Object} field - 时间字段对象
 */
FieldSelectorComponent.prototype.createTimeRangeModal = function(field) {
    // 创建模态框容器
    const modal = this.dom.createElement('div', {
        className: 'time-range-modal',
        id: 'timeRangeModal'
    });

    const modalContent = this.dom.createElement('div', {
        className: 'time-range-modal-content'
    });

    // 模态框头部
    const header = this.dom.createElement('div', {
        className: 'time-range-modal-header'
    });

    const title = this.dom.createElement('h3', {}, `设置时间区间 - ${field.name}`);
    const closeBtn = this.dom.createElement('button', {
        className: 'time-range-modal-close',
        type: 'button'
    }, '×');

    header.appendChild(title);
    header.appendChild(closeBtn);

    // 模态框主体
    const body = this.dom.createElement('div', {
        className: 'time-range-modal-body'
    });

    // 预设时间区间
    const presetsSection = this.createTimeRangePresets(field);
    body.appendChild(presetsSection);

    // 自定义时间区间
    const customSection = this.createCustomTimeRange(field);
    body.appendChild(customSection);

    // 已设置的时间区间列表
    const rangesSection = this.createTimeRangesList(field);
    body.appendChild(rangesSection);

    // 模态框底部
    const footer = this.dom.createElement('div', {
        className: 'time-range-modal-footer'
    });

    const cancelBtn = this.dom.createElement('button', {
        className: 'ios-button ios-button-secondary',
        type: 'button',
        id: 'timeRangeCancelBtn'
    }, '取消');

    const confirmBtn = this.dom.createElement('button', {
        className: 'ios-button ios-button-primary',
        type: 'button',
        id: 'timeRangeConfirmBtn'
    }, '确定');

    footer.appendChild(cancelBtn);
    footer.appendChild(confirmBtn);

    // 组装模态框
    modalContent.appendChild(header);
    modalContent.appendChild(body);
    modalContent.appendChild(footer);
    modal.appendChild(modalContent);

    // 添加到页面
    document.body.appendChild(modal);

    // 绑定事件
    this.bindTimeRangeModalEvents(modal, field);

    // 显示模态框
    setTimeout(() => {
        modal.classList.add('show');
    }, 10);
};

/**
 * @function FieldSelectorComponent.prototype.createTimeRangePresets
 * @description 创建预设时间区间选择
 * @param {Object} field - 时间字段对象
 * @returns {HTMLElement} 预设区间元素
 */
FieldSelectorComponent.prototype.createTimeRangePresets = function(field) {
    const section = this.dom.createElement('div', {
        className: 'time-range-presets'
    });

    const title = this.dom.createElement('h4', {}, '常用时间区间');
    section.appendChild(title);

    const presets = [
        { name: '上午', start: '09:00', end: '12:00' },
        { name: '下午', start: '13:00', end: '18:00' },
        { name: '晚上', start: '19:00', end: '23:00' },
        { name: '全天', start: '00:00', end: '23:59' },
        { name: '工作时间', start: '09:00', end: '18:00' },
        { name: '营业时间', start: '08:00', end: '22:00' }
    ];

    const presetsGrid = this.dom.createElement('div', {
        className: 'time-range-presets-grid'
    });

    for (let i = 0; i < presets.length; i++) {
        const preset = presets[i];
        const presetBtn = this.dom.createElement('button', {
            className: 'time-range-preset-btn',
            type: 'button',
            dataset: {
                name: preset.name,
                start: preset.start,
                end: preset.end
            }
        }, `${preset.name}<br><small>${preset.start}-${preset.end}</small>`);

        presetsGrid.appendChild(presetBtn);
    }

    section.appendChild(presetsGrid);
    return section;
};

/**
 * @function FieldSelectorComponent.prototype.createCustomTimeRange
 * @description 创建自定义时间区间输入
 * @param {Object} field - 时间字段对象
 * @returns {HTMLElement} 自定义区间元素
 */
FieldSelectorComponent.prototype.createCustomTimeRange = function(field) {
    const section = this.dom.createElement('div', {
        className: 'time-range-custom'
    });

    const title = this.dom.createElement('h4', {}, '自定义时间区间');
    section.appendChild(title);

    const form = this.dom.createElement('div', {
        className: 'time-range-form'
    });

    // 区间名称
    const nameRow = this.dom.createElement('div', {
        className: 'time-range-form-row'
    });
    const nameLabel = this.dom.createElement('label', {}, '区间名称');
    const nameInput = this.dom.createElement('input', {
        type: 'text',
        className: 'ios-form-input',
        id: 'customRangeName',
        placeholder: '例如：午休时间'
    });
    nameRow.appendChild(nameLabel);
    nameRow.appendChild(nameInput);

    // 开始时间
    const startRow = this.dom.createElement('div', {
        className: 'time-range-form-row'
    });
    const startLabel = this.dom.createElement('label', {}, '开始时间');
    const startInput = this.dom.createElement('input', {
        type: 'time',
        className: 'ios-form-input',
        id: 'customRangeStart'
    });
    startRow.appendChild(startLabel);
    startRow.appendChild(startInput);

    // 结束时间
    const endRow = this.dom.createElement('div', {
        className: 'time-range-form-row'
    });
    const endLabel = this.dom.createElement('label', {}, '结束时间');
    const endInput = this.dom.createElement('input', {
        type: 'time',
        className: 'ios-form-input',
        id: 'customRangeEnd'
    });
    endRow.appendChild(endLabel);
    endRow.appendChild(endInput);

    // 添加按钮
    const addBtn = this.dom.createElement('button', {
        className: 'ios-button ios-button-secondary',
        type: 'button',
        id: 'addCustomRangeBtn'
    }, '添加区间');

    form.appendChild(nameRow);
    form.appendChild(startRow);
    form.appendChild(endRow);
    form.appendChild(addBtn);

    section.appendChild(form);
    return section;
};

/**
 * @function FieldSelectorComponent.prototype.createTimeRangesList
 * @description 创建已设置的时间区间列表
 * @param {Object} field - 时间字段对象
 * @returns {HTMLElement} 区间列表元素
 */
FieldSelectorComponent.prototype.createTimeRangesList = function(field) {
    const section = this.dom.createElement('div', {
        className: 'time-ranges-list'
    });

    const title = this.dom.createElement('h4', {}, '已设置的时间区间');
    section.appendChild(title);

    const list = this.dom.createElement('div', {
        className: 'time-ranges-items',
        id: 'timeRangesList'
    });

    // 显示现有的时间区间
    this.updateTimeRangesList(field, list);

    section.appendChild(list);
    return section;
};

/**
 * @function FieldSelectorComponent.prototype.bindTimeRangeModalEvents
 * @description 绑定时间区间模态框事件
 * @param {HTMLElement} modal - 模态框元素
 * @param {Object} field - 时间字段对象
 */
FieldSelectorComponent.prototype.bindTimeRangeModalEvents = function(modal, field) {
    const self = this;

    // 关闭按钮事件
    const closeBtn = modal.querySelector('.time-range-modal-close');
    if (closeBtn) {
        closeBtn.addEventListener('click', function() {
            self.closeTimeRangeModal();
        });
    }

    // 取消按钮事件
    const cancelBtn = modal.querySelector('#timeRangeCancelBtn');
    if (cancelBtn) {
        cancelBtn.addEventListener('click', function() {
            self.closeTimeRangeModal();
        });
    }

    // 确定按钮事件
    const confirmBtn = modal.querySelector('#timeRangeConfirmBtn');
    if (confirmBtn) {
        confirmBtn.addEventListener('click', function() {
            self.confirmTimeRanges(field);
        });
    }

    // 预设按钮事件
    const presetBtns = modal.querySelectorAll('.time-range-preset-btn');
    for (let i = 0; i < presetBtns.length; i++) {
        presetBtns[i].addEventListener('click', function() {
            self.addPresetTimeRange(field, this.dataset);
        });
        this.dom.addTouchFeedback(presetBtns[i], 'light');
    }

    // 添加自定义区间按钮事件
    const addCustomBtn = modal.querySelector('#addCustomRangeBtn');
    if (addCustomBtn) {
        addCustomBtn.addEventListener('click', function() {
            self.addCustomTimeRange(field);
        });
    }

    // 背景点击关闭
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            self.closeTimeRangeModal();
        }
    });
};

/**
 * @function FieldSelectorComponent.prototype.addPresetTimeRange
 * @description 添加预设时间区间
 * @param {Object} field - 时间字段对象
 * @param {Object} preset - 预设数据
 */
FieldSelectorComponent.prototype.addPresetTimeRange = function(field, preset) {
    if (!field.timeRanges) {
        field.timeRanges = [];
    }

    // 检查是否已存在相同的区间
    const exists = field.timeRanges.some(range =>
        range.start === preset.start && range.end === preset.end
    );

    if (exists) {
        this.eventBus.emit(SmartOffice.Events.UI_TOAST_SHOW, '该时间区间已存在', 'warning');
        return;
    }

    // 添加新区间
    field.timeRanges.push({
        name: preset.name,
        start: preset.start,
        end: preset.end,
        type: 'preset'
    });

    // 更新列表显示
    const list = document.getElementById('timeRangesList');
    if (list) {
        this.updateTimeRangesList(field, list);
    }

    SmartOffice.log('info', '已添加预设时间区间:', preset.name);
};

/**
 * @function FieldSelectorComponent.prototype.addCustomTimeRange
 * @description 添加自定义时间区间
 * @param {Object} field - 时间字段对象
 */
FieldSelectorComponent.prototype.addCustomTimeRange = function(field) {
    const nameInput = document.getElementById('customRangeName');
    const startInput = document.getElementById('customRangeStart');
    const endInput = document.getElementById('customRangeEnd');

    if (!nameInput || !startInput || !endInput) {
        return;
    }

    const name = nameInput.value.trim();
    const start = startInput.value;
    const end = endInput.value;

    // 验证输入
    if (!name) {
        this.eventBus.emit(SmartOffice.Events.UI_TOAST_SHOW, '请输入区间名称', 'warning');
        return;
    }

    if (!start || !end) {
        this.eventBus.emit(SmartOffice.Events.UI_TOAST_SHOW, '请选择开始和结束时间', 'warning');
        return;
    }

    if (start >= end) {
        this.eventBus.emit(SmartOffice.Events.UI_TOAST_SHOW, '开始时间必须早于结束时间', 'warning');
        return;
    }

    if (!field.timeRanges) {
        field.timeRanges = [];
    }

    // 检查是否已存在相同的区间
    const exists = field.timeRanges.some(range =>
        range.start === start && range.end === end
    );

    if (exists) {
        this.eventBus.emit(SmartOffice.Events.UI_TOAST_SHOW, '该时间区间已存在', 'warning');
        return;
    }

    // 添加新区间
    field.timeRanges.push({
        name: name,
        start: start,
        end: end,
        type: 'custom'
    });

    // 清空输入框
    nameInput.value = '';
    startInput.value = '';
    endInput.value = '';

    // 更新列表显示
    const list = document.getElementById('timeRangesList');
    if (list) {
        this.updateTimeRangesList(field, list);
    }

    SmartOffice.log('info', '已添加自定义时间区间:', name);
};

/**
 * @function FieldSelectorComponent.prototype.updateTimeRangesList
 * @description 更新时间区间列表显示
 * @param {Object} field - 时间字段对象
 * @param {HTMLElement} listElement - 列表容器元素
 */
FieldSelectorComponent.prototype.updateTimeRangesList = function(field, listElement) {
    if (!listElement) return;

    listElement.innerHTML = '';

    if (!field.timeRanges || field.timeRanges.length === 0) {
        listElement.innerHTML = '<div class="time-ranges-empty">暂无设置的时间区间</div>';
        return;
    }

    for (let i = 0; i < field.timeRanges.length; i++) {
        const range = field.timeRanges[i];
        const item = this.dom.createElement('div', {
            className: 'time-range-item'
        });

        const info = this.dom.createElement('div', {
            className: 'time-range-info'
        });

        const name = this.dom.createElement('span', {
            className: 'time-range-name'
        }, range.name);

        const time = this.dom.createElement('span', {
            className: 'time-range-time'
        }, `${range.start} - ${range.end}`);

        const removeBtn = this.dom.createElement('button', {
            className: 'time-range-remove',
            type: 'button',
            dataset: { index: i }
        }, '×');

        info.appendChild(name);
        info.appendChild(time);
        item.appendChild(info);
        item.appendChild(removeBtn);

        listElement.appendChild(item);

        // 绑定删除事件
        const self = this;
        removeBtn.addEventListener('click', function() {
            self.removeTimeRange(field, parseInt(this.dataset.index));
        });
    }
};

/**
 * @function FieldSelectorComponent.prototype.removeTimeRange
 * @description 移除时间区间
 * @param {Object} field - 时间字段对象
 * @param {number} index - 区间索引
 */
FieldSelectorComponent.prototype.removeTimeRange = function(field, index) {
    if (!field.timeRanges || index < 0 || index >= field.timeRanges.length) {
        return;
    }

    field.timeRanges.splice(index, 1);

    // 更新列表显示
    const list = document.getElementById('timeRangesList');
    if (list) {
        this.updateTimeRangesList(field, list);
    }

    SmartOffice.log('info', '已移除时间区间，索引:', index);
};

/**
 * @function FieldSelectorComponent.prototype.confirmTimeRanges
 * @description 确认时间区间设置
 * @param {Object} field - 时间字段对象
 */
FieldSelectorComponent.prototype.confirmTimeRanges = function(field) {
    // 更新字段选择器显示
    this.refresh();

    // 关闭模态框
    this.closeTimeRangeModal();

    // 触发时间区间更新事件
    this.eventBus.emit(SmartOffice.Events.TIME_RANGE_UPDATED, {
        field: field,
        timeRanges: field.timeRanges || []
    });

    SmartOffice.log('info', '时间区间设置已确认:', field.name, field.timeRanges);
};

/**
 * @function FieldSelectorComponent.prototype.closeTimeRangeModal
 * @description 关闭时间区间设置模态框
 */
FieldSelectorComponent.prototype.closeTimeRangeModal = function() {
    const modal = document.getElementById('timeRangeModal');
    if (modal) {
        modal.classList.remove('show');
        setTimeout(() => {
            modal.remove();
        }, 300);
    }
};

// 注册到全局命名空间
SmartOffice.Components.FieldSelector = FieldSelectorComponent;

SmartOffice.log('info', 'SmartOffice字段选择器组件模块初始化完成');
