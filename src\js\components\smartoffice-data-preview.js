/**
 * @file SmartOffice数据预览组件
 * @description iOS风格的数据预览组件，显示上传文件的数据样本和字段信息
 * <AUTHOR> Team
 * @version 1.0.0
 */

/**
 * @function DataPreviewComponent
 * @description 数据预览组件构造函数
 * @constructor
 * @param {Object} options - 配置选项
 * @param {string} options.containerId - 容器元素ID
 * @param {number} options.maxRows - 最大显示行数
 * @param {boolean} options.showFieldTypes - 是否显示字段类型
 */
function DataPreviewComponent(options) {
    // 默认配置
    this.options = SmartOffice.Utils.Helpers.extend({
        containerId: 'dataPreviewContainer',
        maxRows: 10,
        showFieldTypes: true,
        collapsible: true
    }, options || {});

    // 组件状态
    this.isVisible = false;
    this.isExpanded = false;
    this.currentData = null;
    this.currentFile = null;

    // DOM元素引用
    this.containerElement = null;
    this.headerElement = null;
    this.contentElement = null;
    this.toggleButton = null;
    this.infoElement = null;
    this.tableElement = null;

    // 依赖注入
    this.dom = SmartOffice.Utils.DOM;
    this.helpers = SmartOffice.Utils.Helpers;
    this.eventBus = SmartOffice.Core.EventBus;

    SmartOffice.log('info', 'DataPreviewComponent初始化完成');
}

/**
 * @function DataPreviewComponent.prototype.init
 * @description 初始化数据预览组件
 * @returns {boolean} 初始化是否成功
 */
DataPreviewComponent.prototype.init = function() {
    try {
        // 获取容器元素
        this.containerElement = document.getElementById(this.options.containerId);
        if (!this.containerElement) {
            throw new Error('找不到数据预览容器元素: ' + this.options.containerId);
        }

        // 渲染组件界面
        this.render();

        // 绑定事件监听器
        this.bindEvents();

        SmartOffice.log('info', '数据预览组件初始化成功');
        return true;
    } catch (error) {
        SmartOffice.log('error', '数据预览组件初始化失败:', error);
        return false;
    }
};

/**
 * @function DataPreviewComponent.prototype.render
 * @description 渲染数据预览界面
 */
DataPreviewComponent.prototype.render = function() {
    const html = `
        <div class="data-preview-section" style="display: none;">
            <div class="ios-form-header collapsible" id="dataPreviewHeader">
                <h3 class="ios-form-title">数据预览</h3>
                <button type="button" class="preview-toggle-button" id="previewToggleButton">展开</button>
            </div>
            <div class="data-preview-content" id="dataPreviewContent" style="display: none;">
                <div class="data-info" id="dataInfo">
                    <!-- 数据信息将在这里显示 -->
                </div>
                <div class="data-table-container" id="dataTableContainer">
                    <!-- 数据表格将在这里显示 -->
                </div>
            </div>
        </div>
    `;

    this.containerElement.innerHTML = html;

    // 获取DOM元素引用
    this.headerElement = document.getElementById('dataPreviewHeader');
    this.contentElement = document.getElementById('dataPreviewContent');
    this.toggleButton = document.getElementById('previewToggleButton');
    this.infoElement = document.getElementById('dataInfo');
    this.tableElement = document.getElementById('dataTableContainer');
};

/**
 * @function DataPreviewComponent.prototype.bindEvents
 * @description 绑定事件监听器
 */
DataPreviewComponent.prototype.bindEvents = function() {
    const self = this;

    // 切换按钮事件
    if (this.toggleButton) {
        this.toggleButton.addEventListener('click', function() {
            self.toggle();
        });
        this.dom.addTouchFeedback(this.toggleButton, 'light');
    }

    // 头部点击事件（整个头部都可以点击）
    if (this.headerElement) {
        this.headerElement.addEventListener('click', function() {
            self.toggle();
        });
    }
};

/**
 * @function DataPreviewComponent.prototype.show
 * @description 显示数据预览
 * @param {Object} data - 数据对象
 * @param {File} file - 文件对象
 */
DataPreviewComponent.prototype.show = function(data, file) {
    this.currentData = data;
    this.currentFile = file;

    // 显示预览区域
    const previewSection = this.containerElement.querySelector('.data-preview-section');
    if (previewSection) {
        this.dom.setStyle(previewSection, { display: 'block' });
    }

    // 更新数据信息
    this.updateDataInfo();

    // 更新数据表格
    this.updateDataTable();

    // 设置为可见状态
    this.isVisible = true;

    SmartOffice.log('info', '数据预览已显示');
};

/**
 * @function DataPreviewComponent.prototype.hide
 * @description 隐藏数据预览
 */
DataPreviewComponent.prototype.hide = function() {
    const previewSection = this.containerElement.querySelector('.data-preview-section');
    if (previewSection) {
        this.dom.setStyle(previewSection, { display: 'none' });
    }

    this.isVisible = false;
    this.isExpanded = false;

    SmartOffice.log('info', '数据预览已隐藏');
};

/**
 * @function DataPreviewComponent.prototype.toggle
 * @description 切换预览内容的展开/收起状态
 */
DataPreviewComponent.prototype.toggle = function() {
    if (!this.isVisible) return;

    // 触发触觉反馈
    SmartOffice.triggerHapticFeedback('light');

    if (this.isExpanded) {
        // 收起
        this.dom.setStyle(this.contentElement, { display: 'none' });
        this.toggleButton.textContent = '展开';
        this.isExpanded = false;
    } else {
        // 展开
        this.dom.setStyle(this.contentElement, { display: 'block' });
        this.toggleButton.textContent = '收起';
        this.isExpanded = true;
    }

    SmartOffice.log('info', '数据预览切换:', this.isExpanded ? '展开' : '收起');
};

/**
 * @function DataPreviewComponent.prototype.updateDataInfo
 * @description 更新数据信息显示
 */
DataPreviewComponent.prototype.updateDataInfo = function() {
    if (!this.currentData || !this.currentFile || !this.infoElement) return;

    const fileSize = this.formatFileSize(this.currentFile.size);
    const rowCount = this.currentData.data ? this.currentData.data.length : 0;
    const fieldCount = this.currentData.headers ? this.currentData.headers.length : 0;

    const html = `
        <div class="data-info-grid">
            <div class="data-info-item">
                <span class="data-info-label">文件名</span>
                <span class="data-info-value">${this.helpers.escapeHtml(this.currentFile.name)}</span>
            </div>
            <div class="data-info-item">
                <span class="data-info-label">文件大小</span>
                <span class="data-info-value">${fileSize}</span>
            </div>
            <div class="data-info-item">
                <span class="data-info-label">数据行数</span>
                <span class="data-info-value">${rowCount}行</span>
            </div>
            <div class="data-info-item">
                <span class="data-info-label">字段数量</span>
                <span class="data-info-value">${fieldCount}个</span>
            </div>
        </div>
    `;

    this.infoElement.innerHTML = html;
};

/**
 * @function DataPreviewComponent.prototype.updateDataTable
 * @description 更新数据表格显示
 */
DataPreviewComponent.prototype.updateDataTable = function() {
    if (!this.currentData || !this.tableElement) return;

    const data = this.currentData.data || [];
    const headers = this.currentData.headers || [];

    if (headers.length === 0) {
        this.tableElement.innerHTML = '<div class="data-table-empty">暂无数据</div>';
        return;
    }

    // 限制显示的行数
    const displayData = data.slice(0, this.options.maxRows);

    let html = '<div class="data-table-wrapper">';
    html += '<table class="data-preview-table">';

    // 表头
    html += '<thead><tr>';
    for (let i = 0; i < headers.length; i++) {
        const header = headers[i];
        const fieldType = this.detectFieldType(header, data);
        html += `<th class="field-type-${fieldType}">
            <span class="field-name">${this.helpers.escapeHtml(header)}</span>
            ${this.options.showFieldTypes ? this.renderFieldTypeTag(fieldType) : ''}
        </th>`;
    }
    html += '</tr></thead>';

    // 表体
    html += '<tbody>';
    for (let i = 0; i < displayData.length; i++) {
        html += '<tr>';
        for (let j = 0; j < headers.length; j++) {
            const header = headers[j];
            const value = displayData[i][header] || '';
            html += `<td>${this.helpers.escapeHtml(String(value))}</td>`;
        }
        html += '</tr>';
    }
    html += '</tbody>';
    html += '</table>';

    // 如果有更多数据，显示提示
    if (data.length > this.options.maxRows) {
        html += `<div class="data-table-more">
            还有 ${data.length - this.options.maxRows} 行数据未显示
        </div>`;
    }

    html += '</div>';
    this.tableElement.innerHTML = html;
};

/**
 * @function DataPreviewComponent.prototype.detectFieldType
 * @description 检测字段类型
 * @param {string} fieldName - 字段名称
 * @param {Array} data - 数据样本
 * @returns {string} 字段类型
 */
DataPreviewComponent.prototype.detectFieldType = function(fieldName, data) {
    // 简化的类型检测逻辑
    const name = fieldName.toLowerCase();

    if (name.includes('amount') || name.includes('price') || name.includes('total') ||
        name.includes('count') || name.includes('number') || name.includes('qty')) {
        return 'number';
    }

    if (name.includes('date') || name.includes('time') || name.includes('created')) {
        return 'date';
    }

    if (name.includes('category') || name.includes('type') || name.includes('status')) {
        return 'category';
    }

    return 'text';
};

/**
 * @function DataPreviewComponent.prototype.renderFieldTypeTag
 * @description 渲染字段类型标签
 * @param {string} type - 字段类型
 * @returns {string} 类型标签HTML
 */
DataPreviewComponent.prototype.renderFieldTypeTag = function(type) {
    const typeLabels = {
        'number': '数值',
        'date': '日期',
        'category': '分类',
        'text': '文本'
    };

    const label = typeLabels[type] || '文本';
    return `<span class="field-type-tag">${label}</span>`;
};

/**
 * @function DataPreviewComponent.prototype.formatFileSize
 * @description 格式化文件大小
 * @param {number} bytes - 字节数
 * @returns {string} 格式化后的文件大小
 */
DataPreviewComponent.prototype.formatFileSize = function(bytes) {
    if (bytes === 0) return '0 B';

    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * @function DataPreviewComponent.prototype.destroy
 * @description 销毁组件
 */
DataPreviewComponent.prototype.destroy = function() {
    // 清理事件监听器
    if (this.toggleButton) {
        this.toggleButton.removeEventListener('click', this.toggle);
    }

    if (this.headerElement) {
        this.headerElement.removeEventListener('click', this.toggle);
    }

    // 清空容器
    if (this.containerElement) {
        this.containerElement.innerHTML = '';
    }

    // 清理引用
    this.containerElement = null;
    this.headerElement = null;
    this.contentElement = null;
    this.toggleButton = null;
    this.infoElement = null;
    this.tableElement = null;
    this.currentData = null;
    this.currentFile = null;

    SmartOffice.log('info', '数据预览组件已销毁');
};

// 注册到全局命名空间
SmartOffice.Components.DataPreview = DataPreviewComponent;

SmartOffice.log('info', 'SmartOffice数据预览组件模块初始化完成');
