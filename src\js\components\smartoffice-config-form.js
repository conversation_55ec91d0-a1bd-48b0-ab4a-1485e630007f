/**
 * @file SmartOffice配置表单组件
 * @description iOS风格的透视表配置表单组件
 * <AUTHOR> Team
 */

/**
 * @function ConfigFormComponent
 * @description 配置表单组件构造函数
 * @param {Element} container - 容器元素
 * @constructor
 */
function ConfigFormComponent(container) {
    /**
     * @property {Element} container - 组件容器
     */
    this.container = container;

    /**
     * @property {Object} currentConfig - 当前编辑的配置
     */
    this.currentConfig = null;

    /**
     * @property {boolean} isEditMode - 是否为编辑模式
     */
    this.isEditMode = false;

    /**
     * @property {Object} formData - 表单数据
     */
    this.formData = {
        id: '',
        name: '',
        description: '',
        rowFields: [],
        columnFields: [],
        valueFields: [],
        filterFields: [],
        aggregationType: 'sum',
        timeRangeConfigs: {} // 时间字段区间配置
    };

    /**
     * @property {Object} eventBus - 事件总线引用
     */
    this.eventBus = SmartOffice.Core.EventBus;

    /**
     * @property {Object} storage - 存储管理器引用
     */
    this.storage = SmartOffice.Core.Storage;

    /**
     * @property {Object} helpers - 工具函数引用
     */
    this.helpers = SmartOffice.Utils.Helpers;

    /**
     * @property {Object} dom - DOM工具引用
     */
    this.dom = SmartOffice.Utils.DOM;

    /**
     * @property {Object} formElements - 表单元素引用
     */
    this.formElements = {};

    /**
     * @property {Object} uploadedData - 上传的文件数据
     */
    this.uploadedData = null;

    /**
     * @property {Array} availableFields - 可选字段列表
     */
    this.availableFields = [];

    /**
     * @property {Object} fileUploadComponent - 文件上传组件实例
     */
    this.fileUploadComponent = null;

    /**
     * @property {Object} fieldSelectorComponent - 字段选择器组件实例
     */
    this.fieldSelectorComponent = null;

    /**
     * @property {boolean} dataPreviewVisible - 数据预览是否可见
     */
    this.dataPreviewVisible = false;

    // 初始化组件
    this.init();

    SmartOffice.log('info', 'ConfigFormComponent实例创建完成');
}

/**
 * @function ConfigFormComponent.prototype.init
 * @description 初始化组件
 */
ConfigFormComponent.prototype.init = function() {
    SmartOffice.log('info', '开始初始化配置表单组件');
    SmartOffice.log('info', '容器元素:', this.container ? '找到' : '未找到');
    
    if (!this.container) {
        SmartOffice.log('error', '配置表单容器不存在，无法初始化');
        return;
    }
    
    try {
        // 渲染表单界面
        SmartOffice.log('info', '开始渲染表单界面');
        this.render();
        
        // 初始化子组件
        SmartOffice.log('info', '开始初始化子组件');
        this.initSubComponents();
        
        // 绑定事件
        SmartOffice.log('info', '开始绑定事件');
        this.bindEvents();
        
        SmartOffice.log('info', '配置表单组件初始化完成');
        
    } catch (error) {
        SmartOffice.log('error', '配置表单组件初始化失败:', error);
        throw error;
    }
};

/**
 * @function ConfigFormComponent.prototype.initSubComponents
 * @description 初始化子组件
 */
ConfigFormComponent.prototype.initSubComponents = function() {
    const self = this;

    try {
        // 使用setTimeout确保DOM完全渲染后再初始化组件
        setTimeout(function() {
            // 确保文件上传容器存在
            const uploadContainer = document.getElementById('configFormFileUpload');
            if (!uploadContainer) {
                SmartOffice.log('error', '文件上传容器不存在, ID: configFormFileUpload');
                SmartOffice.log('debug', '当前容器内容:', self.container.innerHTML);
                return;
            }

            SmartOffice.log('info', '找到文件上传容器，开始初始化文件上传组件');

            // 初始化文件上传组件 - 使用全局配置支持所有格式
            self.fileUploadComponent = new SmartOffice.Components.FileUpload({
                containerId: 'configFormFileUpload',
                acceptedTypes: SmartOffice.Config.SUPPORTED_FILE_TYPES, // 使用全局配置：['csv', 'txt', 'xlsx', 'xls']
                maxSize: SmartOffice.Config.MAX_FILE_SIZE, // 使用全局配置：50MB
                onUpload: function(result, file) {
                    self.handleFileUploadComplete(result, file);
                },
                onError: function(error) {
                    self.eventBus.emit(SmartOffice.Events.UI_TOAST_SHOW, '文件上传失败: ' + error, 'error');
                }
            });

            // 初始化文件上传组件
            const uploadInitSuccess = self.fileUploadComponent.init();
            if (!uploadInitSuccess) {
                SmartOffice.log('error', '文件上传组件初始化失败');
            } else {
                SmartOffice.log('info', '文件上传组件初始化成功');
            }

            // 创建字段选择器容器（如果不存在）
            if (!document.getElementById('fieldSelectorContainer')) {
                const fieldSelectorContainer = self.dom.createElement('div', {
                    id: 'fieldSelectorContainer',
                    className: 'field-selector-global-container'
                });
                document.body.appendChild(fieldSelectorContainer);
            }

            // 初始化字段选择器组件
            self.fieldSelectorComponent = new SmartOffice.Components.FieldSelector({
                containerId: 'fieldSelectorContainer',
                fields: [],
                multiple: true,
                onSelect: function(selectedFields, fieldType) {
                    self.handleFieldSelectionComplete(selectedFields, fieldType);
                },
                onCancel: function() {
                    SmartOffice.log('info', '字段选择已取消');
                }
            });

            // 初始化字段选择器组件
            const fieldSelectorInitSuccess = self.fieldSelectorComponent.init();
            if (!fieldSelectorInitSuccess) {
                SmartOffice.log('error', '字段选择器组件初始化失败');
            }

            SmartOffice.log('info', '配置表单子组件初始化完成');
        }, 100); // 等待100ms确保DOM渲染完成

    } catch (error) {
        SmartOffice.log('error', '子组件初始化失败:', error);
    }
};

/**
 * @function ConfigFormComponent.prototype.render
 * @description 渲染表单界面
 */
ConfigFormComponent.prototype.render = function() {
    if (!this.container) {
        SmartOffice.log('error', '配置表单容器不存在');
        return;
    }

    // 获取或创建页面内容容器
    let pageContent = this.container.querySelector('.page-content');
    if (!pageContent) {
        SmartOffice.log('warn', '页面内容容器不存在，创建新的');
        pageContent = this.dom.createElement('div', { className: 'page-content' });
        this.container.appendChild(pageContent);
    }

    // 清空容器
    pageContent.innerHTML = '';

    // 创建表单结构
    const form = this.createFormStructure();
    pageContent.appendChild(form);

    // 获取表单元素引用
    this.getFormElementReferences();

    SmartOffice.log('info', '配置表单界面渲染完成');
};

/**
 * @function ConfigFormComponent.prototype.createFormStructure
 * @description 创建表单结构
 * @returns {Element} 表单元素
 */
ConfigFormComponent.prototype.createFormStructure = function() {
    const form = this.dom.createElement('form', {
        className: 'config-form',
        id: 'configForm'
    });

    // 表单标题区域
    const titleSection = this.createTitleSection();
    form.appendChild(titleSection);

    // 基本信息区域
    const basicSection = this.createBasicInfoSection();
    form.appendChild(basicSection);

    // 数据源区域
    const dataSourceSection = this.createDataSourceSection();
    form.appendChild(dataSourceSection);

    // 数据预览区域
    const dataPreviewSection = this.createDataPreviewSection();
    form.appendChild(dataPreviewSection);

    // 字段配置区域
    const fieldsSection = this.createFieldsSection();
    form.appendChild(fieldsSection);

    // 聚合设置区域
    const aggregationSection = this.createAggregationSection();
    form.appendChild(aggregationSection);

    // 操作按钮区域
    const actionsSection = this.createActionsSection();
    form.appendChild(actionsSection);

    return form;
};

/**
 * @function ConfigFormComponent.prototype.createTitleSection
 * @description 创建标题区域
 * @returns {Element} 标题区域元素
 */
ConfigFormComponent.prototype.createTitleSection = function() {
    const section = this.dom.createElement('div', { className: 'form-section form-title-section' });

    const title = this.dom.createElement('h2', {
        className: 'form-title',
        id: 'formTitle'
    }, '新建透视表配置');

    section.appendChild(title);
    return section;
};

/**
 * @function ConfigFormComponent.prototype.createBasicInfoSection
 * @description 创建基本信息区域
 * @returns {Element} 基本信息区域元素
 */
ConfigFormComponent.prototype.createBasicInfoSection = function() {
    const section = this.dom.createElement('div', { className: 'form-section' });

    // 区域标题
    const header = this.dom.createElement('div', { className: 'ios-form-header' });
    const headerTitle = this.dom.createElement('h3', { className: 'ios-form-title' }, '基本信息');
    header.appendChild(headerTitle);
    section.appendChild(header);

    // 表单组
    const formGroup = this.dom.createElement('div', { className: 'ios-form-group' });

    // 配置名称
    const nameRow = this.createFormRow('配置名称', 'input', {
        id: 'configName',
        type: 'text',
        placeholder: '请输入配置名称',
        required: true,
        maxlength: 50
    });
    formGroup.appendChild(nameRow);

    // 配置描述
    const descRow = this.createFormRow('描述', 'textarea', {
        id: 'configDescription',
        placeholder: '请输入配置描述（可选）',
        rows: 3,
        maxlength: 200
    });
    formGroup.appendChild(descRow);

    section.appendChild(formGroup);
    return section;
};

/**
 * @function ConfigFormComponent.prototype.createDataSourceSection
 * @description 创建数据源区域
 * @returns {Element} 数据源区域元素
 */
ConfigFormComponent.prototype.createDataSourceSection = function() {
    const section = this.dom.createElement('div', { className: 'form-section' });

    // 区域标题
    const header = this.dom.createElement('div', { className: 'ios-form-header' });
    const headerTitle = this.dom.createElement('h3', { className: 'ios-form-title' }, '数据源');
    header.appendChild(headerTitle);
    section.appendChild(header);

    // 表单组
    const formGroup = this.dom.createElement('div', { className: 'ios-form-group' });

    // 文件上传容器
    const uploadContainer = this.dom.createElement('div', {
        className: 'file-upload-container',
        id: 'configFormFileUpload'
    });
    formGroup.appendChild(uploadContainer);

    section.appendChild(formGroup);
    return section;
};

/**
 * @function ConfigFormComponent.prototype.createDataPreviewSection
 * @description 创建数据预览区域
 * @returns {Element} 数据预览区域元素
 */
ConfigFormComponent.prototype.createDataPreviewSection = function() {
    const section = this.dom.createElement('div', {
        className: 'form-section data-preview-section',
        id: 'dataPreviewSection',
        style: 'display: none;'
    });

    // 区域标题
    const header = this.dom.createElement('div', { className: 'ios-form-header collapsible' });
    const headerTitle = this.dom.createElement('h3', { className: 'ios-form-title' }, '数据预览');
    const toggleButton = this.dom.createElement('button', {
        type: 'button',
        className: 'preview-toggle-button',
        id: 'previewToggleButton'
    }, '展开');

    header.appendChild(headerTitle);
    header.appendChild(toggleButton);
    section.appendChild(header);

    // 预览内容容器
    const previewContent = this.dom.createElement('div', {
        className: 'data-preview-content',
        id: 'dataPreviewContent',
        style: 'display: none;'
    });

    // 数据信息
    const dataInfo = this.dom.createElement('div', {
        className: 'data-info',
        id: 'dataInfo'
    });
    previewContent.appendChild(dataInfo);

    // 数据表格
    const dataTable = this.dom.createElement('div', {
        className: 'data-table-container',
        id: 'dataTableContainer'
    });
    previewContent.appendChild(dataTable);

    section.appendChild(previewContent);
    return section;
};

/**
 * @function ConfigFormComponent.prototype.createFieldsSection
 * @description 创建字段配置区域
 * @returns {Element} 字段配置区域元素
 */
ConfigFormComponent.prototype.createFieldsSection = function() {
    const section = this.dom.createElement('div', { className: 'form-section' });

    // 区域标题
    const header = this.dom.createElement('div', { className: 'ios-form-header' });
    const headerTitle = this.dom.createElement('h3', { className: 'ios-form-title' }, '字段配置');
    header.appendChild(headerTitle);
    section.appendChild(header);

    // 表单组
    const formGroup = this.dom.createElement('div', { className: 'ios-form-group' });

    // 行字段选择
    const rowFieldsRow = this.createFieldSelectorRow('行字段', 'rowFields', '选择行字段');
    formGroup.appendChild(rowFieldsRow);

    // 列字段选择
    const columnFieldsRow = this.createFieldSelectorRow('列字段', 'columnFields', '选择列字段');
    formGroup.appendChild(columnFieldsRow);

    // 值字段选择
    const valueFieldsRow = this.createFieldSelectorRow('值字段', 'valueFields', '选择值字段');
    formGroup.appendChild(valueFieldsRow);

    // 筛选字段选择
    const filterFieldsRow = this.createFieldSelectorRow('筛选字段', 'filterFields', '选择筛选字段');
    formGroup.appendChild(filterFieldsRow);

    section.appendChild(formGroup);
    return section;
};

/**
 * @function ConfigFormComponent.prototype.createAggregationSection
 * @description 创建聚合设置区域
 * @returns {Element} 聚合设置区域元素
 */
ConfigFormComponent.prototype.createAggregationSection = function() {
    const section = this.dom.createElement('div', { className: 'form-section' });

    // 区域标题
    const header = this.dom.createElement('div', { className: 'ios-form-header' });
    const headerTitle = this.dom.createElement('h3', { className: 'ios-form-title' }, '聚合设置');
    header.appendChild(headerTitle);
    section.appendChild(header);

    // 表单组
    const formGroup = this.dom.createElement('div', { className: 'ios-form-group' });

    // 聚合类型选择
    const aggregationRow = this.createFormRow('聚合方式', 'select', {
        id: 'aggregationType'
    });

    // 添加选项
    const select = aggregationRow.querySelector('select');
    const options = [
        { value: 'sum', text: '求和' },
        { value: 'count', text: '计数' },
        { value: 'avg', text: '平均值' },
        { value: 'min', text: '最小值' },
        { value: 'max', text: '最大值' },
        { value: 'custom', text: '自定义公式' }
    ];

    for (let i = 0; i < options.length; i++) {
        const option = this.dom.createElement('option', {
            value: options[i].value
        }, options[i].text);
        select.appendChild(option);
    }

    formGroup.appendChild(aggregationRow);

    // 自定义公式输入区域（初始隐藏）
    const customFormulaRow = this.createCustomFormulaRow();
    formGroup.appendChild(customFormulaRow);
    section.appendChild(formGroup);
    return section;
};

/**
 * @function ConfigFormComponent.prototype.createActionsSection
 * @description 创建操作按钮区域
 * @returns {Element} 操作按钮区域元素
 */
ConfigFormComponent.prototype.createActionsSection = function() {
    const section = this.dom.createElement('div', { className: 'form-section form-actions' });

    // 保存按钮
    const saveButton = this.dom.createElement('button', {
        type: 'button',
        className: 'ios-button ios-button-primary haptic-medium',
        id: 'saveConfigButton'
    }, '保存配置');

    // 取消按钮
    const cancelButton = this.dom.createElement('button', {
        type: 'button',
        className: 'ios-button ios-button-secondary haptic-light',
        id: 'cancelConfigButton'
    }, '取消');

    // 删除按钮（编辑模式下显示）
    const deleteButton = this.dom.createElement('button', {
        type: 'button',
        className: 'ios-button ios-button-destructive haptic-heavy',
        id: 'deleteConfigButton',
        style: 'display: none;'
    }, '删除配置');

    section.appendChild(saveButton);
    section.appendChild(cancelButton);
    section.appendChild(deleteButton);

    return section;
};

/**
 * @function ConfigFormComponent.prototype.createFormRow
 * @description 创建表单行
 * @param {string} label - 标签文本
 * @param {string} inputType - 输入类型 ('input', 'textarea', 'select')
 * @param {Object} attributes - 输入元素属性
 * @returns {Element} 表单行元素
 */
ConfigFormComponent.prototype.createFormRow = function(label, inputType, attributes) {
    const row = this.dom.createElement('div', { className: 'ios-form-row' });

    // 标签
    const labelEl = this.dom.createElement('label', {
        className: 'ios-form-label',
        'for': attributes.id
    }, label);

    // 输入元素
    let inputEl;
    switch (inputType) {
        case 'textarea':
            inputEl = this.dom.createElement('textarea', Object.assign({
                className: 'ios-form-input'
            }, attributes));
            break;
        case 'select':
            inputEl = this.dom.createElement('select', Object.assign({
                className: 'ios-form-input'
            }, attributes));
            break;
        default:
            inputEl = this.dom.createElement('input', Object.assign({
                className: 'ios-form-input'
            }, attributes));
    }

    row.appendChild(labelEl);
    row.appendChild(inputEl);

    return row;
};

/**
 * @function ConfigFormComponent.prototype.createFieldSelectorRow
 * @description 创建字段选择器行
 * @param {string} label - 标签文本
 * @param {string} fieldName - 字段名称
 * @param {string} placeholder - 占位符文本
 * @returns {Element} 字段选择器行元素
 */
ConfigFormComponent.prototype.createFieldSelectorRow = function(label, fieldName, placeholder) {
    const row = this.dom.createElement('div', { className: 'ios-form-row field-selector-row' });

    // 标签
    const labelEl = this.dom.createElement('label', {
        className: 'ios-form-label'
    }, label);

    // 字段选择器容器
    const selectorContainer = this.dom.createElement('div', {
        className: 'field-selector-container'
    });

    // 选择按钮
    const selectButton = this.dom.createElement('button', {
        type: 'button',
        className: 'field-selector-button touchable',
        dataset: { fieldType: fieldName }
    }, placeholder);

    // 已选字段显示区域
    const selectedFields = this.dom.createElement('div', {
        className: 'selected-fields',
        id: fieldName + 'Display'
    });

    selectorContainer.appendChild(selectButton);
    selectorContainer.appendChild(selectedFields);

    row.appendChild(labelEl);
    row.appendChild(selectorContainer);

    return row;
};

/**
 * @function ConfigFormComponent.prototype.createCustomFormulaRow
 * @description 创建自定义公式输入行
 * @returns {HTMLElement} 自定义公式行元素
 */
ConfigFormComponent.prototype.createCustomFormulaRow = function() {
    const row = this.dom.createElement('div', {
        className: 'ios-form-row custom-formula-row',
        id: 'customFormulaRow',
        style: 'display: none;'
    });

    // 公式输入标签
    const labelElement = this.dom.createElement('label', {
        className: 'ios-form-label',
        'for': 'customFormula'
    }, '自定义公式');

    // 公式输入框
    const inputElement = this.dom.createElement('input', {
        type: 'text',
        className: 'ios-form-input',
        id: 'customFormula',
        placeholder: '例如: =1-(成本/收入)',
        autocomplete: 'off'
    });

    // 帮助文本
    const helpText = this.dom.createElement('div', {
        className: 'form-help-text'
    }, '支持基本数学运算：+、-、*、/、()，使用字段名进行计算');

    // 字段选择器按钮
    const fieldSelectorBtn = this.dom.createElement('button', {
        type: 'button',
        className: 'ios-button ios-button-secondary field-selector-btn',
        id: 'formulaFieldSelector'
    }, '选择字段');

    // 公式验证提示
    const validationMsg = this.dom.createElement('div', {
        className: 'validation-message',
        id: 'formulaValidation',
        style: 'display: none;'
    });

    row.appendChild(labelElement);
    row.appendChild(inputElement);
    row.appendChild(fieldSelectorBtn);
    row.appendChild(helpText);
    row.appendChild(validationMsg);

    return row;
};

/**
 * @function ConfigFormComponent.prototype.getFormElementReferences
 * @description 获取表单元素引用
 */
ConfigFormComponent.prototype.getFormElementReferences = function() {
    // 在容器内查找表单元素
    this.formElements = {
        form: this.container.querySelector('#configForm'),
        title: this.container.querySelector('#formTitle'),
        name: this.container.querySelector('#configName'),
        description: this.container.querySelector('#configDescription'),
        aggregationType: this.container.querySelector('#aggregationType'),
        customFormulaRow: this.container.querySelector('#customFormulaRow'),
        customFormula: this.container.querySelector('#customFormula'),
        formulaFieldSelector: this.container.querySelector('#formulaFieldSelector'),
        formulaValidation: this.container.querySelector('#formulaValidation'),
        saveButton: this.container.querySelector('#saveConfigButton'),
        cancelButton: this.container.querySelector('#cancelConfigButton'),
        deleteButton: this.container.querySelector('#deleteConfigButton'),
        rowFieldsDisplay: this.container.querySelector('#rowFieldsDisplay'),
        columnFieldsDisplay: this.container.querySelector('#columnFieldsDisplay'),
        valueFieldsDisplay: this.container.querySelector('#valueFieldsDisplay'),
        filterFieldsDisplay: this.container.querySelector('#filterFieldsDisplay')
    };

    SmartOffice.log('info', '表单元素引用已获取', {
        form: !!this.formElements.form,
        name: !!this.formElements.name,
        saveButton: !!this.formElements.saveButton
    });
};

/**
 * @function ConfigFormComponent.prototype.bindEvents
 * @description 绑定事件监听器
 */
ConfigFormComponent.prototype.bindEvents = function() {
    const self = this;

    // 表单提交事件
    if (this.formElements.form) {
        this.formElements.form.addEventListener('submit', function(event) {
            event.preventDefault();
            self.handleSave();
        });
    }

    // 保存按钮事件
    if (this.formElements.saveButton) {
        this.formElements.saveButton.addEventListener('click', function() {
            self.handleSave();
        });
        this.dom.addTouchFeedback(this.formElements.saveButton, 'medium');
    }

    // 取消按钮事件
    if (this.formElements.cancelButton) {
        this.formElements.cancelButton.addEventListener('click', function() {
            self.handleCancel();
        });
        this.dom.addTouchFeedback(this.formElements.cancelButton, 'light');
    }

    // 删除按钮事件
    if (this.formElements.deleteButton) {
        this.formElements.deleteButton.addEventListener('click', function() {
            self.handleDelete();
        });
        this.dom.addTouchFeedback(this.formElements.deleteButton, 'heavy');
    }

    // 字段选择器事件
    const fieldSelectors = this.container.querySelectorAll('.field-selector-button');
    for (let i = 0; i < fieldSelectors.length; i++) {
        fieldSelectors[i].addEventListener('click', function() {
            self.handleFieldSelection(this.dataset.fieldType);
        });
        this.dom.addTouchFeedback(fieldSelectors[i], 'light');
    }

    // 表单输入事件
    if (this.formElements.name) {
        this.formElements.name.addEventListener('input', function() {
            self.validateForm();
        });
    }

    // 聚合类型变化事件
    if (this.formElements.aggregationType) {
        this.formElements.aggregationType.addEventListener('change', function() {
            self.handleAggregationTypeChange();
        });
    }

    // 自定义公式输入事件
    if (this.formElements.customFormula) {
        this.formElements.customFormula.addEventListener('input', function() {
            self.validateCustomFormula();
        });
    }

    // 公式字段选择器事件
    if (this.formElements.formulaFieldSelector) {
        this.formElements.formulaFieldSelector.addEventListener('click', function() {
            self.showFormulaFieldSelector();
        });
        this.dom.addTouchFeedback(this.formElements.formulaFieldSelector, 'light');
    }

    // 数据预览切换按钮事件
    const previewToggleButton = document.getElementById('previewToggleButton');
    if (previewToggleButton) {
        previewToggleButton.addEventListener('click', function() {
            self.toggleDataPreview();
        });
        this.dom.addTouchFeedback(previewToggleButton, 'light');
    }

    // 监听路由事件
    this.eventBus.on('configForm:loadData', function(config) {
        self.loadConfigData(config);
    });

    this.eventBus.on('configForm:createNew', function() {
        self.createNewConfig();
    });

    this.eventBus.on('configForm:cleanup', function() {
        self.cleanup();
    });

    // 监听时间区间更新事件
    this.eventBus.on(SmartOffice.Events.TIME_RANGE_UPDATED, function(data) {
        self.handleTimeRangeUpdate(data);
    });

    SmartOffice.log('info', '配置表单事件绑定完成');
};

/**
 * @function ConfigFormComponent.prototype.handleSave
 * @description 处理保存操作
 */
ConfigFormComponent.prototype.handleSave = function() {
    // 触发触觉反馈
    SmartOffice.triggerHapticFeedback('medium');

    // 验证表单
    if (!this.validateForm()) {
        this.eventBus.emit(SmartOffice.Events.UI_TOAST_SHOW, '请填写必填字段', 'warning');
        return;
    }

    // 收集表单数据
    this.collectFormData();

    // 保存配置
    try {
        if (this.isEditMode) {
            // 更新现有配置
            this.formData.updatedAt = new Date().toISOString();
            this.eventBus.emit(SmartOffice.Events.CONFIG_UPDATE, this.formData);
            this.eventBus.emit(SmartOffice.Events.UI_TOAST_SHOW, '配置已更新', 'success');
        } else {
            // 创建新配置
            this.formData.id = this.helpers.generateId('config');
            this.formData.createdAt = new Date().toISOString();
            this.formData.updatedAt = new Date().toISOString();
            this.eventBus.emit(SmartOffice.Events.CONFIG_CREATE, this.formData);
            this.eventBus.emit(SmartOffice.Events.UI_TOAST_SHOW, '配置已创建', 'success');
        }

        // 保存成功后跳转到主页
        SmartOffice.Core.Router.navigate('configList');

    } catch (error) {
        SmartOffice.log('error', '保存配置失败:', error);
        this.eventBus.emit(SmartOffice.Events.UI_TOAST_SHOW, '保存失败，请重试', 'error');
    }
};

/**
 * @function ConfigFormComponent.prototype.handleCancel
 * @description 处理取消操作
 */
ConfigFormComponent.prototype.handleCancel = function() {
    // 触发触觉反馈
    SmartOffice.triggerHapticFeedback('light');

    // 检查是否有未保存的更改
    if (this.hasUnsavedChanges()) {
        if (confirm('有未保存的更改，确定要离开吗？')) {
            SmartOffice.Core.Router.back();
        }
    } else {
        SmartOffice.Core.Router.back();
    }
};

/**
 * @function ConfigFormComponent.prototype.handleDelete
 * @description 处理删除操作
 */
ConfigFormComponent.prototype.handleDelete = function() {
    // 触发触觉反馈
    SmartOffice.triggerHapticFeedback('heavy');

    if (!this.currentConfig) return;

    if (confirm('确定要删除配置 "' + this.currentConfig.name + '" 吗？此操作不可撤销。')) {
        this.eventBus.emit(SmartOffice.Events.CONFIG_DELETE, this.currentConfig.id);
        this.eventBus.emit(SmartOffice.Events.UI_TOAST_SHOW, '配置已删除', 'success');
        SmartOffice.Core.Router.navigate('configList');
    }
};

/**
 * @function ConfigFormComponent.prototype.handleFieldSelection
 * @description 处理字段选择
 * @param {string} fieldType - 字段类型
 */
ConfigFormComponent.prototype.handleFieldSelection = function(fieldType) {
    // 触发触觉反馈
    SmartOffice.triggerHapticFeedback('light');

    // 检查是否有可选字段
    if (!this.availableFields || this.availableFields.length === 0) {
        this.eventBus.emit(SmartOffice.Events.UI_TOAST_SHOW, '请先上传CSV文件', 'warning');
        return;
    }

    // 显示字段选择器
    this.showFieldSelector(fieldType);

    SmartOffice.log('info', '字段选择: ' + fieldType);
};

/**
 * @function ConfigFormComponent.prototype.validateForm
 * @description 验证表单数据
 * @returns {boolean} 是否验证通过
 */
ConfigFormComponent.prototype.validateForm = function() {
    let isValid = true;

    // 验证配置名称
    if (!this.formElements.name || !this.formElements.name.value.trim()) {
        this.markFieldError(this.formElements.name, '配置名称不能为空');
        isValid = false;
    } else {
        this.clearFieldError(this.formElements.name);
    }

    // 验证配置名称长度
    if (this.formElements.name && this.formElements.name.value.length > 50) {
        this.markFieldError(this.formElements.name, '配置名称不能超过50个字符');
        isValid = false;
    }

    // 验证描述长度
    if (this.formElements.description && this.formElements.description.value.length > 200) {
        this.markFieldError(this.formElements.description, '描述不能超过200个字符');
        isValid = false;
    } else if (this.formElements.description) {
        this.clearFieldError(this.formElements.description);
    }

    // 验证自定义公式
    if (this.formElements.aggregationType && this.formElements.aggregationType.value === 'custom') {
        if (!this.formElements.customFormula || !this.formElements.customFormula.value.trim()) {
            this.markFieldError(this.formElements.customFormula, '请输入自定义公式');
            isValid = false;
        } else if (!this.validateCustomFormula()) {
            isValid = false;
        } else {
            this.clearFieldError(this.formElements.customFormula);
        }
    }

    return isValid;
};

/**
 * @function ConfigFormComponent.prototype.markFieldError
 * @description 标记字段错误
 * @param {Element} field - 字段元素
 * @param {string} message - 错误消息
 */
ConfigFormComponent.prototype.markFieldError = function(field, message) {
    if (!field) return;

    this.dom.addClass(field, 'error');

    // 移除之前的错误提示
    const existingError = field.parentNode.querySelector('.field-error');
    if (existingError) {
        existingError.remove();
    }

    // 添加错误提示
    const errorEl = this.dom.createElement('div', {
        className: 'field-error'
    }, message);

    field.parentNode.appendChild(errorEl);
};

/**
 * @function ConfigFormComponent.prototype.clearFieldError
 * @description 清除字段错误
 * @param {Element} field - 字段元素
 */
ConfigFormComponent.prototype.clearFieldError = function(field) {
    if (!field) return;

    this.dom.removeClass(field, 'error');

    // 移除错误提示
    const errorEl = field.parentNode.querySelector('.field-error');
    if (errorEl) {
        errorEl.remove();
    }
};

/**
 * @function ConfigFormComponent.prototype.collectFormData
 * @description 收集表单数据
 */
ConfigFormComponent.prototype.collectFormData = function() {
    this.formData.name = this.formElements.name ? this.formElements.name.value.trim() : '';
    this.formData.description = this.formElements.description ? this.formElements.description.value.trim() : '';
    this.formData.aggregationType = this.formElements.aggregationType ? this.formElements.aggregationType.value : 'sum';

    // 收集自定义公式数据
    if (this.formData.aggregationType === 'custom') {
        this.formData.customFormula = this.formElements.customFormula ? this.formElements.customFormula.value.trim() : '';
    } else {
        this.formData.customFormula = '';
    }

    // 收集时间区间配置数据
    this.collectTimeRangeConfigs();

    SmartOffice.log('info', '表单数据已收集', this.formData);
};

/**
 * @function ConfigFormComponent.prototype.loadConfigData
 * @description 加载配置数据到表单
 * @param {Object} config - 配置对象
 */
ConfigFormComponent.prototype.loadConfigData = function(config) {
    if (!config) return;

    this.currentConfig = config;
    this.isEditMode = true;
    this.formData = this.helpers.deepClone(config);

    // 更新表单标题
    if (this.formElements.title) {
        this.formElements.title.textContent = '编辑透视表配置';
    }

    // 填充表单字段
    if (this.formElements.name) {
        this.formElements.name.value = config.name || '';
    }

    if (this.formElements.description) {
        this.formElements.description.value = config.description || '';
    }

    if (this.formElements.aggregationType) {
        this.formElements.aggregationType.value = config.aggregationType || 'sum';
        // 触发聚合类型变化事件以显示/隐藏自定义公式
        this.handleAggregationTypeChange();
    }

    // 加载自定义公式
    if (this.formElements.customFormula && config.customFormula) {
        this.formElements.customFormula.value = config.customFormula;
    }

    // 加载时间区间配置
    if (config.timeRangeConfigs) {
        this.loadTimeRangeConfigs(config.timeRangeConfigs);
    }

    // 显示删除按钮
    if (this.formElements.deleteButton) {
        this.dom.show(this.formElements.deleteButton);
    }

    // 更新字段显示
    this.updateFieldDisplays();

    SmartOffice.log('info', '配置数据已加载到表单', config);
};

/**
 * @function ConfigFormComponent.prototype.createNewConfig
 * @description 创建新配置
 */
ConfigFormComponent.prototype.createNewConfig = function() {
    this.currentConfig = null;
    this.isEditMode = false;

    // 重置表单数据
    this.formData = {
        id: '',
        name: '',
        description: '',
        rowFields: [],
        columnFields: [],
        valueFields: [],
        filterFields: [],
        aggregationType: 'sum',
        timeRangeConfigs: {}
    };

    // 更新表单标题
    if (this.formElements.title) {
        this.formElements.title.textContent = '新建透视表配置';
    }

    // 清空表单字段
    if (this.formElements.name) {
        this.formElements.name.value = '';
    }

    if (this.formElements.description) {
        this.formElements.description.value = '';
    }

    if (this.formElements.aggregationType) {
        this.formElements.aggregationType.value = 'sum';
    }

    // 隐藏删除按钮
    if (this.formElements.deleteButton) {
        this.dom.hide(this.formElements.deleteButton);
    }

    // 清空字段显示
    this.updateFieldDisplays();

    SmartOffice.log('info', '新建配置模式已激活');
};

/**
 * @function ConfigFormComponent.prototype.updateFieldDisplays
 * @description 更新字段显示
 */
ConfigFormComponent.prototype.updateFieldDisplays = function() {
    const fieldTypes = ['rowFields', 'columnFields', 'valueFields', 'filterFields'];

    for (let i = 0; i < fieldTypes.length; i++) {
        const fieldType = fieldTypes[i];
        const displayEl = this.formElements[fieldType + 'Display'];
        const fields = this.formData[fieldType] || [];

        if (displayEl) {
            displayEl.innerHTML = '';

            if (fields.length > 0) {
                for (let j = 0; j < fields.length; j++) {
                    const tag = this.createFieldTag(fields[j], fieldType);
                    displayEl.appendChild(tag);
                }
            }
        }
    }
};

/**
 * @function ConfigFormComponent.prototype.createFieldTag
 * @description 创建字段标签
 * @param {string} fieldName - 字段名称
 * @param {string} fieldType - 字段类型
 * @returns {Element} 字段标签元素
 */
ConfigFormComponent.prototype.createFieldTag = function(fieldName, fieldType) {
    const tag = this.dom.createElement('span', {
        className: 'field-tag ' + fieldType.replace('Fields', ''),
        dataset: { fieldName: fieldName, fieldType: fieldType }
    });

    const text = this.dom.createElement('span', {}, fieldName);
    const removeBtn = this.dom.createElement('button', {
        type: 'button',
        className: 'field-tag-remove'
    }, '×');

    // 绑定移除事件
    const self = this;
    removeBtn.addEventListener('click', function(event) {
        event.stopPropagation();
        self.removeField(fieldName, fieldType);
    });

    tag.appendChild(text);
    tag.appendChild(removeBtn);

    return tag;
};

/**
 * @function ConfigFormComponent.prototype.removeField
 * @description 移除字段
 * @param {string} fieldName - 字段名称
 * @param {string} fieldType - 字段类型
 */
ConfigFormComponent.prototype.removeField = function(fieldName, fieldType) {
    const fields = this.formData[fieldType] || [];
    const index = fields.indexOf(fieldName);

    if (index !== -1) {
        fields.splice(index, 1);
        this.updateFieldDisplays();
        SmartOffice.log('info', '字段已移除: ' + fieldName + ' from ' + fieldType);
    }
};

/**
 * @function ConfigFormComponent.prototype.hasUnsavedChanges
 * @description 检查是否有未保存的更改
 * @returns {boolean} 是否有未保存的更改
 */
ConfigFormComponent.prototype.hasUnsavedChanges = function() {
    if (!this.formElements.name) return false;

    const currentName = this.formElements.name.value.trim();
    const currentDesc = this.formElements.description ? this.formElements.description.value.trim() : '';

    if (this.isEditMode && this.currentConfig) {
        return currentName !== this.currentConfig.name ||
               currentDesc !== (this.currentConfig.description || '');
    } else {
        return currentName !== '' || currentDesc !== '';
    }
};

/**
 * @function ConfigFormComponent.prototype.handleFileUploadComplete
 * @description 处理文件上传完成
 * @param {Object} result - 解析结果
 * @param {File} file - 文件对象
 */
ConfigFormComponent.prototype.handleFileUploadComplete = function(result, file) {
    try {
        // 保存上传的数据
        this.uploadedData = result;

        // 提取可选字段
        this.updateAvailableFields(result);

        // 显示数据预览
        this.showDataPreview(result, file);

        // 显示成功提示
        this.eventBus.emit(SmartOffice.Events.UI_TOAST_SHOW,
            '文件上传成功，共' + result.data.length + '行数据', 'success');

        SmartOffice.log('info', '文件上传完成处理成功', {
            fileName: file.name,
            rowCount: result.data.length,
            fieldCount: this.availableFields.length
        });

    } catch (error) {
        SmartOffice.log('error', '文件上传完成处理失败:', error);
        this.eventBus.emit(SmartOffice.Events.UI_TOAST_SHOW, '数据处理失败，请重试', 'error');
    }
};

/**
 * @function ConfigFormComponent.prototype.updateAvailableFields
 * @description 更新可选字段列表
 * @param {Object} result - 解析结果
 */
ConfigFormComponent.prototype.updateAvailableFields = function(result) {
    this.availableFields = [];

    if (result && result.headers && result.headers.length > 0) {
        for (let i = 0; i < result.headers.length; i++) {
            const header = result.headers[i];
            this.availableFields.push({
                name: header,
                label: header,
                type: this.detectFieldType(header, result.data),
                description: ''
            });
        }
    }

    // 更新字段选择器的可选字段和数据源
    if (this.fieldSelectorComponent) {
        if (this.fieldSelectorComponent.setDataSource) {
            this.fieldSelectorComponent.setDataSource(result);
        }
        if (this.fieldSelectorComponent.setFields) {
            this.fieldSelectorComponent.setFields(this.availableFields);
        }
    }

    SmartOffice.log('info', '可选字段已更新:', this.availableFields.length + '个字段');
};

/**
 * @function ConfigFormComponent.prototype.detectFieldType
 * @description 检测字段类型（基于字段名和数据样本）
 * @param {string} fieldName - 字段名称
 * @param {Array} data - 数据样本
 * @returns {string} 字段类型
 */
ConfigFormComponent.prototype.detectFieldType = function(fieldName, data) {
    if (!fieldName || !data || data.length === 0) {
        return 'text';
    }

    const name = fieldName.toLowerCase();

    // 基于字段名的类型检测
    if (name.includes('amount') || name.includes('price') || name.includes('cost') ||
        name.includes('total') || name.includes('sum') || name.includes('count') ||
        name.includes('quantity') || name.includes('qty') || name.includes('number')) {
        return 'number';
    }

    if (name.includes('date') || name.includes('time') || name.includes('created') ||
        name.includes('updated') || name.includes('modified')) {
        return 'date';
    }

    if (name.includes('category') || name.includes('type') || name.includes('status') ||
        name.includes('level') || name.includes('grade') || name.includes('class')) {
        return 'category';
    }

    // 基于数据内容的类型检测（检查前几行数据）
    const sampleSize = Math.min(10, data.length);
    let numberCount = 0;
    let dateCount = 0;

    for (let i = 0; i < sampleSize; i++) {
        const value = data[i][fieldName];
        if (value !== null && value !== undefined && value !== '') {
            // 检查是否为数字
            if (!isNaN(value) && !isNaN(parseFloat(value))) {
                numberCount++;
            }
            // 检查是否为日期
            else if (this.isDateString(value)) {
                dateCount++;
            }
        }
    }

    // 如果大部分值都是数字，则认为是数值类型
    if (numberCount / sampleSize > 0.7) {
        return 'number';
    }

    // 如果大部分值都是日期，则认为是日期类型
    if (dateCount / sampleSize > 0.7) {
        return 'date';
    }

    return 'text';
};

/**
 * @function ConfigFormComponent.prototype.isDateString
 * @description 检查字符串是否为日期格式
 * @param {string} str - 要检查的字符串
 * @returns {boolean} 是否为日期格式
 */
ConfigFormComponent.prototype.isDateString = function(str) {
    if (typeof str !== 'string') return false;

    const date = new Date(str);
    return !isNaN(date.getTime()) && str.length > 6;
};

/**
 * @function ConfigFormComponent.prototype.showFieldSelector
 * @description 显示字段选择器
 * @param {string} fieldType - 字段类型
 */
ConfigFormComponent.prototype.showFieldSelector = function(fieldType) {
    if (!this.fieldSelectorComponent) {
        SmartOffice.log('error', '字段选择器组件未初始化');
        return;
    }

    // 获取字段类型的中文名称
    const fieldTypeNames = {
        'rowFields': '行字段',
        'columnFields': '列字段',
        'valueFields': '值字段',
        'filterFields': '筛选字段'
    };

    // 配置字段选择器选项
    this.fieldSelectorComponent.options.fieldType = fieldType;
    this.fieldSelectorComponent.options.title = '选择' + (fieldTypeNames[fieldType] || '字段');
    this.fieldSelectorComponent.options.multiple = true;
    this.fieldSelectorComponent.options.maxSelections = fieldType === 'valueFields' ? 5 : 10;

    // 设置当前已选择的字段
    const currentFields = this.formData[fieldType] || [];
    const preSelectedFields = this.availableFields.filter(field =>
        currentFields.includes(field.name)
    );

    // 显示字段选择器
    this.fieldSelectorComponent.show(preSelectedFields);
};

/**
 * @function ConfigFormComponent.prototype.handleFieldSelectionComplete
 * @description 处理字段选择完成
 * @param {Array} selectedFields - 选中的字段
 * @param {string} fieldType - 字段类型
 */
ConfigFormComponent.prototype.handleFieldSelectionComplete = function(selectedFields, fieldType) {
    try {
        // 更新表单数据
        this.formData[fieldType] = selectedFields.map(field => field.name);

        // 更新字段显示
        this.updateFieldDisplays();

        // 显示成功提示
        this.eventBus.emit(SmartOffice.Events.UI_TOAST_SHOW,
            '已选择' + selectedFields.length + '个字段', 'success');

        SmartOffice.log('info', '字段选择完成', {
            fieldType: fieldType,
            selectedCount: selectedFields.length,
            fields: selectedFields.map(f => f.name)
        });

    } catch (error) {
        SmartOffice.log('error', '字段选择处理失败:', error);
        this.eventBus.emit(SmartOffice.Events.UI_TOAST_SHOW, '字段选择处理失败', 'error');
    }
};

/**
 * @function ConfigFormComponent.prototype.showDataPreview
 * @description 显示数据预览
 * @param {Object} result - 解析结果
 * @param {File} file - 文件对象
 */
ConfigFormComponent.prototype.showDataPreview = function(result, file) {
    try {
        // 显示数据预览区域
        const previewSection = document.getElementById('dataPreviewSection');
        if (previewSection) {
            this.dom.setStyle(previewSection, { display: 'block' });
        }

        // 更新数据信息
        this.updateDataInfo(result, file);

        // 更新数据表格
        this.updateDataTable(result);

        // 设置预览可见状态
        this.dataPreviewVisible = true;

        SmartOffice.log('info', '数据预览已显示');

    } catch (error) {
        SmartOffice.log('error', '数据预览显示失败:', error);
    }
};

/**
 * @function ConfigFormComponent.prototype.updateDataInfo
 * @description 更新数据信息显示
 * @param {Object} result - 解析结果
 * @param {File} file - 文件对象
 */
ConfigFormComponent.prototype.updateDataInfo = function(result, file) {
    const dataInfo = document.getElementById('dataInfo');
    if (!dataInfo) return;

    const fileSize = this.formatFileSize(file.size);
    const rowCount = result.data ? result.data.length : 0;
    const fieldCount = result.headers ? result.headers.length : 0;

    const html = `
        <div class="data-info-grid">
            <div class="data-info-item">
                <span class="data-info-label">文件名</span>
                <span class="data-info-value">${this.helpers.escapeHtml(file.name)}</span>
            </div>
            <div class="data-info-item">
                <span class="data-info-label">文件大小</span>
                <span class="data-info-value">${fileSize}</span>
            </div>
            <div class="data-info-item">
                <span class="data-info-label">数据行数</span>
                <span class="data-info-value">${rowCount}行</span>
            </div>
            <div class="data-info-item">
                <span class="data-info-label">字段数量</span>
                <span class="data-info-value">${fieldCount}个</span>
            </div>
        </div>
    `;

    dataInfo.innerHTML = html;
};

/**
 * @function ConfigFormComponent.prototype.formatFileSize
 * @description 格式化文件大小
 * @param {number} bytes - 字节数
 * @returns {string} 格式化后的文件大小
 */
ConfigFormComponent.prototype.formatFileSize = function(bytes) {
    if (bytes === 0) return '0 B';

    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * @function ConfigFormComponent.prototype.updateDataTable
 * @description 更新数据表格显示
 * @param {Object} result - 解析结果
 */
ConfigFormComponent.prototype.updateDataTable = function(result) {
    const tableContainer = document.getElementById('dataTableContainer');
    if (!tableContainer || !result || !result.data || !result.headers) return;

    // 限制显示的行数（移动端优化）
    const maxRows = 10;
    const displayData = result.data.slice(0, maxRows);

    let html = '<div class="data-table-wrapper">';
    html += '<table class="data-preview-table">';

    // 表头
    html += '<thead><tr>';
    for (let i = 0; i < result.headers.length; i++) {
        const header = result.headers[i];
        const fieldType = this.detectFieldType(header, result.data);
        html += `<th class="field-type-${fieldType}">
            <span class="field-name">${this.helpers.escapeHtml(header)}</span>
            <span class="field-type-tag">${this.getFieldTypeLabel(fieldType)}</span>
        </th>`;
    }
    html += '</tr></thead>';

    // 表体
    html += '<tbody>';
    for (let i = 0; i < displayData.length; i++) {
        html += '<tr>';
        for (let j = 0; j < result.headers.length; j++) {
            const header = result.headers[j];
            const value = displayData[i][header] || '';
            html += `<td>${this.helpers.escapeHtml(String(value))}</td>`;
        }
        html += '</tr>';
    }
    html += '</tbody>';
    html += '</table>';

    // 如果有更多数据，显示提示
    if (result.data.length > maxRows) {
        html += `<div class="data-table-more">
            还有 ${result.data.length - maxRows} 行数据未显示
        </div>`;
    }

    html += '</div>';
    tableContainer.innerHTML = html;
};

/**
 * @function ConfigFormComponent.prototype.getFieldTypeLabel
 * @description 获取字段类型标签
 * @param {string} type - 字段类型
 * @returns {string} 类型标签
 */
ConfigFormComponent.prototype.getFieldTypeLabel = function(type) {
    const typeLabels = {
        'number': '数值',
        'date': '日期',
        'category': '分类',
        'text': '文本'
    };
    return typeLabels[type] || '文本';
};

/**
 * @function ConfigFormComponent.prototype.toggleDataPreview
 * @description 切换数据预览显示状态
 */
ConfigFormComponent.prototype.toggleDataPreview = function() {
    const previewContent = document.getElementById('dataPreviewContent');
    const toggleButton = document.getElementById('previewToggleButton');

    if (!previewContent || !toggleButton) return;

    // 触发触觉反馈
    SmartOffice.triggerHapticFeedback('light');

    if (this.dataPreviewVisible) {
        // 隐藏预览
        this.dom.setStyle(previewContent, { display: 'none' });
        toggleButton.textContent = '展开';
        this.dataPreviewVisible = false;
    } else {
        // 显示预览
        this.dom.setStyle(previewContent, { display: 'block' });
        toggleButton.textContent = '收起';
        this.dataPreviewVisible = true;
    }

    SmartOffice.log('info', '数据预览切换:', this.dataPreviewVisible ? '显示' : '隐藏');
};

/**
 * @function ConfigFormComponent.prototype.cleanup
 * @description 清理组件状态
 */
ConfigFormComponent.prototype.cleanup = function() {
    // 清理表单状态
    this.currentConfig = null;
    this.isEditMode = false;

    // 清理上传数据状态
    this.uploadedData = null;
    this.availableFields = [];
    this.dataPreviewVisible = false;

    // 清理子组件
    if (this.fileUploadComponent && this.fileUploadComponent.destroy) {
        this.fileUploadComponent.destroy();
    }

    if (this.fieldSelectorComponent && this.fieldSelectorComponent.destroy) {
        this.fieldSelectorComponent.destroy();
    }

    // 清除所有字段错误
    const errorFields = this.dom.querySelectorAll('.ios-form-input.error');
    for (let i = 0; i < errorFields.length; i++) {
        this.clearFieldError(errorFields[i]);
    }

    SmartOffice.log('info', '配置表单组件已清理');
};

/**
 * @function ConfigFormComponent.prototype.handleAggregationTypeChange
 * @description 处理聚合类型变化
 */
ConfigFormComponent.prototype.handleAggregationTypeChange = function() {
    const aggregationType = this.formElements.aggregationType.value;
    const customFormulaRow = this.formElements.customFormulaRow;

    if (aggregationType === 'custom') {
        // 显示自定义公式输入
        if (customFormulaRow) {
            customFormulaRow.style.display = 'block';
        }
    } else {
        // 隐藏自定义公式输入
        if (customFormulaRow) {
            customFormulaRow.style.display = 'none';
        }
        // 清空公式输入
        if (this.formElements.customFormula) {
            this.formElements.customFormula.value = '';
        }
    }

    SmartOffice.log('info', '聚合类型已变更:', aggregationType);
};

/**
 * @function ConfigFormComponent.prototype.validateCustomFormula
 * @description 验证自定义公式
 */
ConfigFormComponent.prototype.validateCustomFormula = function() {
    const formula = this.formElements.customFormula.value.trim();

    if (!formula) {
        this.hideFormulaValidation();
        return true;
    }

    try {
        // 基本公式格式验证
        const isValid = this.validateFormulaFormat(formula);

        if (isValid) {
            this.showFormulaValidation('公式格式正确', 'success');
            return true;
        } else {
            this.showFormulaValidation('公式格式错误，请检查语法', 'error');
            return false;
        }
    } catch (error) {
        this.showFormulaValidation('公式验证失败: ' + error.message, 'error');
        return false;
    }
};

/**
 * @function ConfigFormComponent.prototype.validateFormulaFormat
 * @description 验证公式格式
 * @param {string} formula - 公式字符串
 * @returns {boolean} 是否有效
 */
ConfigFormComponent.prototype.validateFormulaFormat = function(formula) {
    // 移除等号开头
    if (formula.startsWith('=')) {
        formula = formula.substring(1);
    }

    // 检查基本字符（字母、数字、运算符、括号、空格）
    const validChars = /^[a-zA-Z0-9\u4e00-\u9fa5+\-*/().\s]+$/;
    if (!validChars.test(formula)) {
        return false;
    }

    // 检查括号匹配
    let parenthesesCount = 0;
    for (let i = 0; i < formula.length; i++) {
        if (formula[i] === '(') parenthesesCount++;
        if (formula[i] === ')') parenthesesCount--;
        if (parenthesesCount < 0) return false;
    }

    return parenthesesCount === 0;
};

/**
 * @function ConfigFormComponent.prototype.showFormulaValidation
 * @description 显示公式验证消息
 * @param {string} message - 验证消息
 * @param {string} type - 消息类型 ('success', 'error', 'warning')
 */
ConfigFormComponent.prototype.showFormulaValidation = function(message, type) {
    const validationMsg = this.formElements.formulaValidation;
    if (!validationMsg) return;

    validationMsg.textContent = message;
    validationMsg.className = 'validation-message ' + type;
    validationMsg.style.display = 'block';
};

/**
 * @function ConfigFormComponent.prototype.hideFormulaValidation
 * @description 隐藏公式验证消息
 */
ConfigFormComponent.prototype.hideFormulaValidation = function() {
    const validationMsg = this.formElements.formulaValidation;
    if (!validationMsg) return;

    validationMsg.style.display = 'none';
};

/**
 * @function ConfigFormComponent.prototype.showFormulaFieldSelector
 * @description 显示公式字段选择器
 */
ConfigFormComponent.prototype.showFormulaFieldSelector = function() {
    // 触发触觉反馈
    SmartOffice.triggerHapticFeedback('light');

    // 检查是否有可选字段
    if (!this.availableFields || this.availableFields.length === 0) {
        this.eventBus.emit(SmartOffice.Events.UI_TOAST_SHOW, '请先上传数据文件', 'warning');
        return;
    }

    // 创建字段选择弹窗
    this.showFormulaFieldModal();

    SmartOffice.log('info', '显示公式字段选择器');
};

/**
 * @function ConfigFormComponent.prototype.showFormulaFieldModal
 * @description 显示公式字段选择模态框
 */
ConfigFormComponent.prototype.showFormulaFieldModal = function() {
    // 创建模态框
    const modal = this.dom.createElement('div', {
        className: 'ios-modal formula-field-modal',
        id: 'formulaFieldModal'
    });

    const modalContent = this.dom.createElement('div', {
        className: 'ios-modal-content'
    });

    const modalHeader = this.dom.createElement('div', {
        className: 'ios-modal-header'
    });

    const modalTitle = this.dom.createElement('h3', {}, '选择字段');
    const closeBtn = this.dom.createElement('button', {
        className: 'ios-modal-close',
        type: 'button'
    }, '×');

    modalHeader.appendChild(modalTitle);
    modalHeader.appendChild(closeBtn);

    const modalBody = this.dom.createElement('div', {
        className: 'ios-modal-body'
    });

    // 添加字段列表
    for (let i = 0; i < this.availableFields.length; i++) {
        const field = this.availableFields[i];
        const fieldBtn = this.dom.createElement('button', {
            className: 'field-option-btn',
            type: 'button',
            dataset: { fieldName: field.name }
        }, field.name + ' (' + field.type + ')');

        modalBody.appendChild(fieldBtn);
    }

    modalContent.appendChild(modalHeader);
    modalContent.appendChild(modalBody);
    modal.appendChild(modalContent);

    // 添加到页面
    document.body.appendChild(modal);

    // 绑定事件
    const self = this;

    closeBtn.addEventListener('click', function() {
        self.closeFormulaFieldModal();
    });

    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            self.closeFormulaFieldModal();
        }
    });

    const fieldBtns = modalBody.querySelectorAll('.field-option-btn');
    for (let i = 0; i < fieldBtns.length; i++) {
        fieldBtns[i].addEventListener('click', function() {
            self.insertFieldIntoFormula(this.dataset.fieldName);
            self.closeFormulaFieldModal();
        });
        this.dom.addTouchFeedback(fieldBtns[i], 'light');
    }

    // 显示模态框
    setTimeout(function() {
        modal.classList.add('show');
    }, 10);
};

/**
 * @function ConfigFormComponent.prototype.closeFormulaFieldModal
 * @description 关闭公式字段选择模态框
 */
ConfigFormComponent.prototype.closeFormulaFieldModal = function() {
    const modal = document.getElementById('formulaFieldModal');
    if (modal) {
        modal.classList.remove('show');
        setTimeout(function() {
            modal.remove();
        }, 300);
    }
};

/**
 * @function ConfigFormComponent.prototype.insertFieldIntoFormula
 * @description 将字段插入到公式中
 * @param {string} fieldName - 字段名称
 */
ConfigFormComponent.prototype.insertFieldIntoFormula = function(fieldName) {
    const formulaInput = this.formElements.customFormula;
    if (!formulaInput) return;

    const cursorPos = formulaInput.selectionStart;
    const currentValue = formulaInput.value;
    const newValue = currentValue.slice(0, cursorPos) + fieldName + currentValue.slice(cursorPos);

    formulaInput.value = newValue;
    formulaInput.focus();
    formulaInput.setSelectionRange(cursorPos + fieldName.length, cursorPos + fieldName.length);

    // 验证更新后的公式
    this.validateCustomFormula();

    SmartOffice.log('info', '字段已插入公式:', fieldName);
};

/**
 * @function ConfigFormComponent.prototype.handleTimeRangeUpdate
 * @description 处理时间区间更新事件
 * @param {Object} data - 时间区间更新数据
 */
ConfigFormComponent.prototype.handleTimeRangeUpdate = function(data) {
    if (!data || !data.field) {
        return;
    }

    const fieldName = data.field.name;
    const timeRanges = data.timeRanges || [];

    // 保存时间区间配置到表单数据
    if (!this.formData.timeRangeConfigs) {
        this.formData.timeRangeConfigs = {};
    }

    this.formData.timeRangeConfigs[fieldName] = timeRanges;

    // 更新可用字段中的时间区间信息
    for (let i = 0; i < this.availableFields.length; i++) {
        if (this.availableFields[i].name === fieldName) {
            this.availableFields[i].timeRanges = timeRanges;
            break;
        }
    }

    SmartOffice.log('info', '时间区间配置已更新:', fieldName, timeRanges);
};

/**
 * @function ConfigFormComponent.prototype.collectTimeRangeConfigs
 * @description 收集时间区间配置数据
 */
ConfigFormComponent.prototype.collectTimeRangeConfigs = function() {
    if (!this.formData.timeRangeConfigs) {
        this.formData.timeRangeConfigs = {};
    }

    // 遍历所有可用字段，收集时间字段的区间配置
    for (let i = 0; i < this.availableFields.length; i++) {
        const field = this.availableFields[i];
        if (field.type === 'time' && field.timeRanges && field.timeRanges.length > 0) {
            this.formData.timeRangeConfigs[field.name] = field.timeRanges;
        }
    }

    SmartOffice.log('info', '时间区间配置已收集:', this.formData.timeRangeConfigs);
};

/**
 * @function ConfigFormComponent.prototype.loadTimeRangeConfigs
 * @description 加载时间区间配置
 * @param {Object} timeRangeConfigs - 时间区间配置对象
 */
ConfigFormComponent.prototype.loadTimeRangeConfigs = function(timeRangeConfigs) {
    if (!timeRangeConfigs || typeof timeRangeConfigs !== 'object') {
        return;
    }

    // 将时间区间配置应用到可用字段
    for (let i = 0; i < this.availableFields.length; i++) {
        const field = this.availableFields[i];
        if (field.type === 'time' && timeRangeConfigs[field.name]) {
            field.timeRanges = timeRangeConfigs[field.name];
        }
    }

    // 保存到表单数据
    this.formData.timeRangeConfigs = timeRangeConfigs;

    SmartOffice.log('info', '时间区间配置已加载:', timeRangeConfigs);
};

/**
 * @function ConfigFormComponent.prototype.getTimeRangeConfig
 * @description 获取指定字段的时间区间配置
 * @param {string} fieldName - 字段名称
 * @returns {Array} 时间区间数组
 */
ConfigFormComponent.prototype.getTimeRangeConfig = function(fieldName) {
    if (!this.formData.timeRangeConfigs || !fieldName) {
        return [];
    }

    return this.formData.timeRangeConfigs[fieldName] || [];
};

/**
 * @function ConfigFormComponent.prototype.setTimeRangeConfig
 * @description 设置指定字段的时间区间配置
 * @param {string} fieldName - 字段名称
 * @param {Array} timeRanges - 时间区间数组
 */
ConfigFormComponent.prototype.setTimeRangeConfig = function(fieldName, timeRanges) {
    if (!fieldName) {
        return;
    }

    if (!this.formData.timeRangeConfigs) {
        this.formData.timeRangeConfigs = {};
    }

    this.formData.timeRangeConfigs[fieldName] = timeRanges || [];

    // 同时更新可用字段中的配置
    for (let i = 0; i < this.availableFields.length; i++) {
        if (this.availableFields[i].name === fieldName) {
            this.availableFields[i].timeRanges = timeRanges || [];
            break;
        }
    }

    SmartOffice.log('info', '时间区间配置已设置:', fieldName, timeRanges);
};

/**
 * @function ConfigFormComponent.prototype.clearTimeRangeConfigs
 * @description 清空所有时间区间配置
 */
ConfigFormComponent.prototype.clearTimeRangeConfigs = function() {
    this.formData.timeRangeConfigs = {};

    // 清空可用字段中的时间区间配置
    for (let i = 0; i < this.availableFields.length; i++) {
        if (this.availableFields[i].type === 'time') {
            this.availableFields[i].timeRanges = [];
        }
    }

    SmartOffice.log('info', '时间区间配置已清空');
};

// 注册到全局命名空间
SmartOffice.Components.ConfigForm = ConfigFormComponent;

SmartOffice.log('info', 'SmartOffice配置表单组件模块初始化完成');
