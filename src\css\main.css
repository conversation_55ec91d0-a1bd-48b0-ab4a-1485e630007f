/**
 * @file 主样式文件 - iOS风格设计
 * @description GoMyHire移动端快速透视分析的基础样式
 * <AUTHOR> Team
 */

/* 导入组件样式 */
@import url('./components/charts.css');
@import url('./components/templates.css');
@import url('./components/filters.css');

/* CSS变量定义 - iOS设计系统 */
:root {
    /* iOS颜色系统 */
    --ios-blue: #007AFF;
    --ios-green: #34C759;
    --ios-orange: #FF9500;
    --ios-red: #FF3B30;
    --ios-purple: #AF52DE;
    --ios-pink: #FF2D92;
    --ios-teal: #5AC8FA;
    --ios-indigo: #5856D6;
    
    /* 背景颜色 */
    --background-primary: #F2F2F7;
    --background-secondary: #FFFFFF;
    --background-tertiary: #F8F8F8;
    --background-grouped: #F2F2F7;
    
    /* 文本颜色 */
    --text-primary: #000000;
    --text-secondary: #8E8E93;
    --text-tertiary: #C7C7CC;
    --text-quaternary: #D1D1D6;
    
    /* 分隔线颜色 */
    --separator-opaque: #C6C6C8;
    --separator-non-opaque: rgba(60, 60, 67, 0.36);
    
    /* 阴影 */
    --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.15);
    --shadow-heavy: 0 8px 25px rgba(0, 0, 0, 0.2);
    
    /* 圆角 */
    --radius-small: 8px;
    --radius-medium: 12px;
    --radius-large: 16px;
    --radius-xlarge: 20px;
    
    /* 间距 */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --spacing-xxl: 48px;
    
    /* 动画时长 */
    --animation-fast: 0.2s;
    --animation-normal: 0.3s;
    --animation-slow: 0.5s;
    
    /* 字体大小 */
    --font-size-caption: 12px;
    --font-size-footnote: 13px;
    --font-size-subhead: 15px;
    --font-size-callout: 16px;
    --font-size-body: 17px;
    --font-size-headline: 17px;
    --font-size-title3: 20px;
    --font-size-title2: 22px;
    --font-size-title1: 28px;
    --font-size-large-title: 34px;
    
    /* 视口高度修正（iOS Safari） */
    --vh: 1vh;
}

/* 基础重置 */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

*::before,
*::after {
    box-sizing: border-box;
}

/* 基础HTML元素 */
html {
    font-size: 16px;
    line-height: 1.4;
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: transparent;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
    font-size: var(--font-size-body);
    line-height: 1.47;
    color: var(--text-primary);
    background-color: var(--background-primary);
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* iOS状态栏占位 */
.status-bar-spacer {
    height: env(safe-area-inset-top, 20px);
    background-color: var(--background-secondary);
}

/* 主应用容器 */
.app-container {
    min-height: calc(100vh - env(safe-area-inset-top, 20px));
    min-height: calc(var(--vh, 1vh) * 100 - env(safe-area-inset-top, 20px));
    display: flex;
    flex-direction: column;
    background-color: var(--background-primary);
}

/* iOS风格导航栏 */
.nav-bar {
    background-color: var(--background-secondary);
    border-bottom: 0.5px solid var(--separator-opaque);
    position: sticky;
    top: 0;
    z-index: 1000;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.nav-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 44px;
    padding: 0 var(--spacing-md);
    max-width: 100%;
}

.nav-title {
    font-size: var(--font-size-headline);
    font-weight: 600;
    color: var(--text-primary);
    text-align: center;
    flex: 1;
    margin: 0 var(--spacing-sm);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.nav-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 44px;
    height: 44px;
    border: none;
    background: none;
    color: var(--ios-blue);
    cursor: pointer;
    border-radius: var(--radius-small);
    transition: all var(--animation-fast) ease;
    -webkit-tap-highlight-color: transparent;
}

.nav-button:hover {
    background-color: rgba(0, 122, 255, 0.1);
}

.nav-button:active {
    background-color: rgba(0, 122, 255, 0.2);
    transform: scale(0.95);
}

.nav-button:disabled {
    color: var(--text-tertiary);
    cursor: not-allowed;
}

.nav-button:disabled:hover {
    background-color: transparent;
}

.nav-icon {
    width: 24px;
    height: 24px;
    fill: currentColor;
}

/* 主内容区域 */
.main-content {
    flex: 1;
    position: relative;
    overflow: hidden;
}

/* 页面容器 */
.page {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--background-primary);
    transform: translateX(0);
    transition: transform var(--animation-normal) cubic-bezier(0.4, 0.0, 0.2, 1);
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}

.page-hidden {
    transform: translateX(100%);
}

.page-content {
    padding: var(--spacing-md);
    padding-bottom: calc(var(--spacing-md) + env(safe-area-inset-bottom, 0px));
    min-height: 100%;
}

/* iOS风格卡片 */
.card {
    background-color: var(--background-secondary);
    border-radius: var(--radius-medium);
    box-shadow: var(--shadow-light);
    margin-bottom: var(--spacing-md);
    overflow: hidden;
}

.card-content {
    padding: var(--spacing-md);
}

.card-header {
    padding: var(--spacing-md) var(--spacing-md) var(--spacing-sm);
}

.card-title {
    font-size: var(--font-size-headline);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.card-subtitle {
    font-size: var(--font-size-footnote);
    color: var(--text-secondary);
}

/* 空状态 */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: var(--spacing-xxl) var(--spacing-md);
    min-height: 300px;
}

.empty-icon {
    width: 64px;
    height: 64px;
    margin-bottom: var(--spacing-lg);
    opacity: 0.3;
}

.empty-icon svg {
    width: 100%;
    height: 100%;
    fill: var(--text-secondary);
}

.empty-title {
    font-size: var(--font-size-title3);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.empty-description {
    font-size: var(--font-size-subhead);
    color: var(--text-secondary);
    line-height: 1.4;
    max-width: 280px;
}

/* iOS风格加载指示器 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.loading-spinner {
    width: 60px;
    height: 60px;
    background-color: var(--background-secondary);
    border-radius: var(--radius-medium);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-medium);
}

.spinner-ring {
    width: 24px;
    height: 24px;
    border: 2px solid var(--text-quaternary);
    border-top: 2px solid var(--ios-blue);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 375px) {
    .nav-content {
        padding: 0 var(--spacing-sm);
    }
    
    .page-content {
        padding: var(--spacing-sm);
    }
}

@media (min-width: 768px) {
    .app-container {
        max-width: 414px;
        margin: 0 auto;
        box-shadow: var(--shadow-medium);
    }
}

/* 可滚动区域标识 */
.scrollable {
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}

/* 触摸反馈 */
.touchable {
    cursor: pointer;
    transition: all var(--animation-fast) ease;
    -webkit-tap-highlight-color: transparent;
}

.touchable:active {
    transform: scale(0.98);
    opacity: 0.8;
}

/* 自定义公式样式 */
.custom-formula-row {
    margin-top: var(--spacing-md);
    padding: var(--spacing-md);
    background-color: var(--background-tertiary);
    border-radius: var(--radius-medium);
    border: 1px solid var(--separator-opaque);
}

.field-selector-btn {
    margin-top: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    background-color: var(--ios-blue);
    color: white;
    border: none;
    border-radius: var(--radius-small);
    font-size: var(--font-size-footnote);
    cursor: pointer;
    transition: all var(--animation-fast) ease;
}

.field-selector-btn:hover {
    background-color: #0056CC;
}

.field-selector-btn:active {
    transform: scale(0.95);
}

.form-help-text {
    font-size: var(--font-size-caption);
    color: var(--text-secondary);
    margin-top: var(--spacing-xs);
    line-height: 1.3;
}

.validation-message {
    margin-top: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-small);
    font-size: var(--font-size-caption);
    font-weight: 500;
}

.validation-message.success {
    background-color: rgba(52, 199, 89, 0.1);
    color: var(--ios-green);
    border: 1px solid rgba(52, 199, 89, 0.3);
}

.validation-message.error {
    background-color: rgba(255, 59, 48, 0.1);
    color: var(--ios-red);
    border: 1px solid rgba(255, 59, 48, 0.3);
}

.validation-message.warning {
    background-color: rgba(255, 149, 0, 0.1);
    color: var(--ios-orange);
    border: 1px solid rgba(255, 149, 0, 0.3);
}

/* 公式字段选择模态框 */
.formula-field-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all var(--animation-normal) ease;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.formula-field-modal.show {
    opacity: 1;
    visibility: visible;
}

.ios-modal-content {
    background-color: var(--background-secondary);
    border-radius: var(--radius-large);
    box-shadow: var(--shadow-heavy);
    max-width: 320px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    transform: scale(0.9);
    transition: transform var(--animation-normal) ease;
}

.formula-field-modal.show .ios-modal-content {
    transform: scale(1);
}

.ios-modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md);
    border-bottom: 0.5px solid var(--separator-opaque);
}

.ios-modal-header h3 {
    font-size: var(--font-size-headline);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.ios-modal-close {
    width: 32px;
    height: 32px;
    border: none;
    background: none;
    color: var(--text-secondary);
    font-size: 24px;
    cursor: pointer;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--animation-fast) ease;
}

.ios-modal-close:hover {
    background-color: var(--background-tertiary);
}

.ios-modal-body {
    padding: var(--spacing-md);
    max-height: 400px;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}

.field-option-btn {
    display: block;
    width: 100%;
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-xs);
    background-color: var(--background-tertiary);
    border: 1px solid var(--separator-opaque);
    border-radius: var(--radius-medium);
    color: var(--text-primary);
    font-size: var(--font-size-subhead);
    text-align: left;
    cursor: pointer;
    transition: all var(--animation-fast) ease;
}

.field-option-btn:hover {
    background-color: var(--background-grouped);
    border-color: var(--ios-blue);
}

.field-option-btn:active {
    transform: scale(0.98);
    background-color: rgba(0, 122, 255, 0.1);
}

/* 时间区间设置样式 */
.so-time-range-btn {
    width: 24px;
    height: 24px;
    border: none;
    background: none;
    color: var(--ios-blue);
    cursor: pointer;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-left: var(--spacing-xs);
    transition: all var(--animation-fast) ease;
}

.so-time-range-btn:hover {
    background-color: rgba(0, 122, 255, 0.1);
}

.so-time-range-btn:active {
    transform: scale(0.9);
}

.time-range-icon {
    width: 16px;
    height: 16px;
    fill: currentColor;
}

.so-time-ranges {
    margin-top: var(--spacing-xs);
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
}

.so-time-range-tag {
    display: inline-block;
    padding: 2px var(--spacing-xs);
    background-color: rgba(0, 122, 255, 0.1);
    color: var(--ios-blue);
    border-radius: var(--radius-small);
    font-size: var(--font-size-caption);
    font-weight: 500;
}

/* 时间区间模态框样式 */
.time-range-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all var(--animation-normal) ease;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.time-range-modal.show {
    opacity: 1;
    visibility: visible;
}

.time-range-modal-content {
    background-color: var(--background-secondary);
    border-radius: var(--radius-large);
    box-shadow: var(--shadow-heavy);
    max-width: 400px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    transform: scale(0.9);
    transition: transform var(--animation-normal) ease;
}

.time-range-modal.show .time-range-modal-content {
    transform: scale(1);
}

.time-range-modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md);
    border-bottom: 0.5px solid var(--separator-opaque);
}

.time-range-modal-header h3 {
    font-size: var(--font-size-headline);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.time-range-modal-close {
    width: 32px;
    height: 32px;
    border: none;
    background: none;
    color: var(--text-secondary);
    font-size: 24px;
    cursor: pointer;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--animation-fast) ease;
}

.time-range-modal-close:hover {
    background-color: var(--background-tertiary);
}

.time-range-modal-body {
    padding: var(--spacing-md);
    max-height: 500px;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}

.time-range-modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    border-top: 0.5px solid var(--separator-opaque);
}

/* 预设时间区间样式 */
.time-range-presets h4,
.time-range-custom h4,
.time-ranges-list h4 {
    font-size: var(--font-size-subhead);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-sm) 0;
}

.time-range-presets-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
}

.time-range-preset-btn {
    padding: var(--spacing-sm);
    background-color: var(--background-tertiary);
    border: 1px solid var(--separator-opaque);
    border-radius: var(--radius-medium);
    color: var(--text-primary);
    font-size: var(--font-size-footnote);
    text-align: center;
    cursor: pointer;
    transition: all var(--animation-fast) ease;
    line-height: 1.2;
}

.time-range-preset-btn:hover {
    background-color: var(--background-grouped);
    border-color: var(--ios-blue);
}

.time-range-preset-btn:active {
    transform: scale(0.98);
    background-color: rgba(0, 122, 255, 0.1);
}

.time-range-preset-btn small {
    display: block;
    color: var(--text-secondary);
    font-size: var(--font-size-caption);
    margin-top: 2px;
}

/* 自定义时间区间表单样式 */
.time-range-custom {
    margin-bottom: var(--spacing-md);
}

.time-range-form {
    background-color: var(--background-tertiary);
    border-radius: var(--radius-medium);
    padding: var(--spacing-md);
}

.time-range-form-row {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-sm);
}

.time-range-form-row:last-child {
    margin-bottom: 0;
}

.time-range-form-row label {
    flex: 0 0 80px;
    font-size: var(--font-size-footnote);
    font-weight: 500;
    color: var(--text-primary);
    margin-right: var(--spacing-sm);
}

.time-range-form-row input {
    flex: 1;
    padding: var(--spacing-xs) var(--spacing-sm);
    border: 1px solid var(--separator-opaque);
    border-radius: var(--radius-small);
    background-color: var(--background-secondary);
    color: var(--text-primary);
    font-size: var(--font-size-subhead);
}

.time-range-form-row input:focus {
    outline: none;
    border-color: var(--ios-blue);
    box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.2);
}

/* 时间区间列表样式 */
.time-ranges-list {
    margin-bottom: var(--spacing-md);
}

.time-ranges-items {
    max-height: 200px;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}

.time-ranges-empty {
    text-align: center;
    color: var(--text-secondary);
    font-size: var(--font-size-footnote);
    padding: var(--spacing-md);
    font-style: italic;
}

.time-range-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-sm);
    background-color: var(--background-tertiary);
    border-radius: var(--radius-small);
    margin-bottom: var(--spacing-xs);
}

.time-range-item:last-child {
    margin-bottom: 0;
}

.time-range-info {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.time-range-name {
    font-size: var(--font-size-subhead);
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 2px;
}

.time-range-time {
    font-size: var(--font-size-caption);
    color: var(--text-secondary);
}

.time-range-remove {
    width: 24px;
    height: 24px;
    border: none;
    background: none;
    color: var(--ios-red);
    font-size: 18px;
    cursor: pointer;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--animation-fast) ease;
}

.time-range-remove:hover {
    background-color: rgba(255, 59, 48, 0.1);
}

.time-range-remove:active {
    transform: scale(0.9);
}
